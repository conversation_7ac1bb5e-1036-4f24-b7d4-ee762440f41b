(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[178],{745:(e,t,n)=>{"use strict";n.d(t,{Content:()=>ty,Provider:()=>tp,Root:()=>tm,Trigger:()=>tv});var r=n(2115),i=n.t(r,2),o=n(5185),a=n(6101),s=n(6081),l=n(9178),u=n(2712),c=i["useId".toString()]||(()=>void 0),d=0;let f=["top","right","bottom","left"],h=Math.min,p=Math.max,m=Math.round,v=Math.floor,y=e=>({x:e,y:e}),g={left:"right",right:"left",bottom:"top",top:"bottom"},b={start:"end",end:"start"};function w(e,t){return"function"==typeof e?e(t):e}function x(e){return e.split("-")[0]}function E(e){return e.split("-")[1]}function C(e){return"x"===e?"y":"x"}function T(e){return"y"===e?"height":"width"}function R(e){return["top","bottom"].includes(x(e))?"y":"x"}function P(e){return e.replace(/start|end/g,e=>b[e])}function S(e){return e.replace(/left|right|bottom|top/g,e=>g[e])}function O(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function N(e){let{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function A(e,t,n){let r,{reference:i,floating:o}=e,a=R(t),s=C(R(t)),l=T(s),u=x(t),c="y"===a,d=i.x+i.width/2-o.width/2,f=i.y+i.height/2-o.height/2,h=i[l]/2-o[l]/2;switch(u){case"top":r={x:d,y:i.y-o.height};break;case"bottom":r={x:d,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:f};break;case"left":r={x:i.x-o.width,y:f};break;default:r={x:i.x,y:i.y}}switch(E(t)){case"start":r[s]-=h*(n&&c?-1:1);break;case"end":r[s]+=h*(n&&c?-1:1)}return r}let k=async(e,t,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:a}=n,s=o.filter(Boolean),l=await (null==a.isRTL?void 0:a.isRTL(t)),u=await a.getElementRects({reference:e,floating:t,strategy:i}),{x:c,y:d}=A(u,r,l),f=r,h={},p=0;for(let n=0;n<s.length;n++){let{name:o,fn:m}=s[n],{x:v,y:y,data:g,reset:b}=await m({x:c,y:d,initialPlacement:r,placement:f,strategy:i,middlewareData:h,rects:u,platform:a,elements:{reference:e,floating:t}});c=null!=v?v:c,d=null!=y?y:d,h={...h,[o]:{...h[o],...g}},b&&p<=50&&(p++,"object"==typeof b&&(b.placement&&(f=b.placement),b.rects&&(u=!0===b.rects?await a.getElementRects({reference:e,floating:t,strategy:i}):b.rects),{x:c,y:d}=A(u,f,l)),n=-1)}return{x:c,y:d,placement:f,strategy:i,middlewareData:h}};async function D(e,t){var n;void 0===t&&(t={});let{x:r,y:i,platform:o,rects:a,elements:s,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:f=!1,padding:h=0}=w(t,e),p=O(h),m=s[f?"floating"===d?"reference":"floating":d],v=N(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(m)))||n?m:m.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(s.floating)),boundary:u,rootBoundary:c,strategy:l})),y="floating"===d?{x:r,y:i,width:a.floating.width,height:a.floating.height}:a.reference,g=await (null==o.getOffsetParent?void 0:o.getOffsetParent(s.floating)),b=await (null==o.isElement?void 0:o.isElement(g))&&await (null==o.getScale?void 0:o.getScale(g))||{x:1,y:1},x=N(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:y,offsetParent:g,strategy:l}):y);return{top:(v.top-x.top+p.top)/b.y,bottom:(x.bottom-v.bottom+p.bottom)/b.y,left:(v.left-x.left+p.left)/b.x,right:(x.right-v.right+p.right)/b.x}}function M(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function L(e){return f.some(t=>e[t]>=0)}async function j(e,t){let{placement:n,platform:r,elements:i}=e,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),a=x(n),s=E(n),l="y"===R(n),u=["left","top"].includes(a)?-1:1,c=o&&l?-1:1,d=w(t,e),{mainAxis:f,crossAxis:h,alignmentAxis:p}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return s&&"number"==typeof p&&(h="end"===s?-1*p:p),l?{x:h*c,y:f*u}:{x:f*u,y:h*c}}function F(){return"undefined"!=typeof window}function I(e){return H(e)?(e.nodeName||"").toLowerCase():"#document"}function q(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function B(e){var t;return null==(t=(H(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function H(e){return!!F()&&(e instanceof Node||e instanceof q(e).Node)}function _(e){return!!F()&&(e instanceof Element||e instanceof q(e).Element)}function z(e){return!!F()&&(e instanceof HTMLElement||e instanceof q(e).HTMLElement)}function U(e){return!!F()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof q(e).ShadowRoot)}function W(e){let{overflow:t,overflowX:n,overflowY:r,display:i}=Y(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(i)}function K(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function Q(e){let t=G(),n=_(e)?Y(e):e;return"none"!==n.transform||"none"!==n.perspective||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function G(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function V(e){return["html","body","#document"].includes(I(e))}function Y(e){return q(e).getComputedStyle(e)}function $(e){return _(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function X(e){if("html"===I(e))return e;let t=e.assignedSlot||e.parentNode||U(e)&&e.host||B(e);return U(t)?t.host:t}function Z(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let i=function e(t){let n=X(t);return V(n)?t.ownerDocument?t.ownerDocument.body:t.body:z(n)&&W(n)?n:e(n)}(e),o=i===(null==(r=e.ownerDocument)?void 0:r.body),a=q(i);if(o){let e=J(a);return t.concat(a,a.visualViewport||[],W(i)?i:[],e&&n?Z(e):[])}return t.concat(i,Z(i,[],n))}function J(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ee(e){let t=Y(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,i=z(e),o=i?e.offsetWidth:n,a=i?e.offsetHeight:r,s=m(n)!==o||m(r)!==a;return s&&(n=o,r=a),{width:n,height:r,$:s}}function et(e){return _(e)?e:e.contextElement}function en(e){let t=et(e);if(!z(t))return y(1);let n=t.getBoundingClientRect(),{width:r,height:i,$:o}=ee(t),a=(o?m(n.width):n.width)/r,s=(o?m(n.height):n.height)/i;return a&&Number.isFinite(a)||(a=1),s&&Number.isFinite(s)||(s=1),{x:a,y:s}}let er=y(0);function ei(e){let t=q(e);return G()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:er}function eo(e,t,n,r){var i;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=e.getBoundingClientRect(),a=et(e),s=y(1);t&&(r?_(r)&&(s=en(r)):s=en(e));let l=(void 0===(i=n)&&(i=!1),r&&(!i||r===q(a))&&i)?ei(a):y(0),u=(o.left+l.x)/s.x,c=(o.top+l.y)/s.y,d=o.width/s.x,f=o.height/s.y;if(a){let e=q(a),t=r&&_(r)?q(r):r,n=e,i=J(n);for(;i&&r&&t!==n;){let e=en(i),t=i.getBoundingClientRect(),r=Y(i),o=t.left+(i.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(i.clientTop+parseFloat(r.paddingTop))*e.y;u*=e.x,c*=e.y,d*=e.x,f*=e.y,u+=o,c+=a,i=J(n=q(i))}}return N({width:d,height:f,x:u,y:c})}function ea(e,t){let n=$(e).scrollLeft;return t?t.left+n:eo(B(e)).left+n}function es(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=q(e),r=B(e),i=n.visualViewport,o=r.clientWidth,a=r.clientHeight,s=0,l=0;if(i){o=i.width,a=i.height;let e=G();(!e||e&&"fixed"===t)&&(s=i.offsetLeft,l=i.offsetTop)}return{width:o,height:a,x:s,y:l}}(e,n);else if("document"===t)r=function(e){let t=B(e),n=$(e),r=e.ownerDocument.body,i=p(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=p(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+ea(e),s=-n.scrollTop;return"rtl"===Y(r).direction&&(a+=p(t.clientWidth,r.clientWidth)-i),{width:i,height:o,x:a,y:s}}(B(e));else if(_(t))r=function(e,t){let n=eo(e,!0,"fixed"===t),r=n.top+e.clientTop,i=n.left+e.clientLeft,o=z(e)?en(e):y(1),a=e.clientWidth*o.x,s=e.clientHeight*o.y;return{width:a,height:s,x:i*o.x,y:r*o.y}}(t,n);else{let n=ei(e);r={...t,x:t.x-n.x,y:t.y-n.y}}return N(r)}function el(e){return"static"===Y(e).position}function eu(e,t){if(!z(e)||"fixed"===Y(e).position)return null;if(t)return t(e);let n=e.offsetParent;return B(e)===n&&(n=n.ownerDocument.body),n}function ec(e,t){let n=q(e);if(K(e))return n;if(!z(e)){let t=X(e);for(;t&&!V(t);){if(_(t)&&!el(t))return t;t=X(t)}return n}let r=eu(e,t);for(;r&&["table","td","th"].includes(I(r))&&el(r);)r=eu(r,t);return r&&V(r)&&el(r)&&!Q(r)?n:r||function(e){let t=X(e);for(;z(t)&&!V(t);){if(Q(t))return t;if(K(t))break;t=X(t)}return null}(e)||n}let ed=async function(e){let t=this.getOffsetParent||ec,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=z(t),i=B(t),o="fixed"===n,a=eo(e,!0,o,t),s={scrollLeft:0,scrollTop:0},l=y(0);if(r||!r&&!o)if(("body"!==I(t)||W(i))&&(s=$(t)),r){let e=eo(t,!0,o,t);l.x=e.x+t.clientLeft,l.y=e.y+t.clientTop}else i&&(l.x=ea(i));let u=0,c=0;if(i&&!r&&!o){let e=i.getBoundingClientRect();c=e.top+s.scrollTop,u=e.left+s.scrollLeft-ea(i,e)}return{x:a.left+s.scrollLeft-l.x-u,y:a.top+s.scrollTop-l.y-c,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ef={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:i}=e,o="fixed"===i,a=B(r),s=!!t&&K(t.floating);if(r===a||s&&o)return n;let l={scrollLeft:0,scrollTop:0},u=y(1),c=y(0),d=z(r);if((d||!d&&!o)&&(("body"!==I(r)||W(a))&&(l=$(r)),z(r))){let e=eo(r);u=en(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-l.scrollLeft*u.x+c.x,y:n.y*u.y-l.scrollTop*u.y+c.y}},getDocumentElement:B,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:i}=e,o=[..."clippingAncestors"===n?K(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=Z(e,[],!1).filter(e=>_(e)&&"body"!==I(e)),i=null,o="fixed"===Y(e).position,a=o?X(e):e;for(;_(a)&&!V(a);){let t=Y(a),n=Q(a);n||"fixed"!==t.position||(i=null),(o?!n&&!i:!n&&"static"===t.position&&!!i&&["absolute","fixed"].includes(i.position)||W(a)&&!n&&function e(t,n){let r=X(t);return!(r===n||!_(r)||V(r))&&("fixed"===Y(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):i=t,a=X(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],a=o[0],s=o.reduce((e,n)=>{let r=es(t,n,i);return e.top=p(r.top,e.top),e.right=h(r.right,e.right),e.bottom=h(r.bottom,e.bottom),e.left=p(r.left,e.left),e},es(t,a,i));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:ec,getElementRects:ed,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=ee(e);return{width:t,height:n}},getScale:en,isElement:_,isRTL:function(e){return"rtl"===Y(e).direction}},eh=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:i,rects:o,platform:a,elements:s,middlewareData:l}=t,{element:u,padding:c=0}=w(e,t)||{};if(null==u)return{};let d=O(c),f={x:n,y:r},m=C(R(i)),v=T(m),y=await a.getDimensions(u),g="y"===m,b=g?"clientHeight":"clientWidth",x=o.reference[v]+o.reference[m]-f[m]-o.floating[v],P=f[m]-o.reference[m],S=await (null==a.getOffsetParent?void 0:a.getOffsetParent(u)),N=S?S[b]:0;N&&await (null==a.isElement?void 0:a.isElement(S))||(N=s.floating[b]||o.floating[v]);let A=N/2-y[v]/2-1,k=h(d[g?"top":"left"],A),D=h(d[g?"bottom":"right"],A),M=N-y[v]-D,L=N/2-y[v]/2+(x/2-P/2),j=p(k,h(L,M)),F=!l.arrow&&null!=E(i)&&L!==j&&o.reference[v]/2-(L<k?k:D)-y[v]/2<0,I=F?L<k?L-k:L-M:0;return{[m]:f[m]+I,data:{[m]:j,centerOffset:L-j-I,...F&&{alignmentOffset:I}},reset:F}}}),ep=(e,t,n)=>{let r=new Map,i={platform:ef,...n},o={...i.platform,_c:r};return k(e,t,{...i,platform:o})};var em=n(7650),ev="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function ey(e,t){let n,r,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ey(e[r],t[r]))return!1;return!0}if((n=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!e.$$typeof)&&!ey(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eg(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eb(e,t){let n=eg(e);return Math.round(t*n)/n}function ew(e){let t=r.useRef(e);return ev(()=>{t.current=e}),t}let ex=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eh({element:n.current,padding:r}).fn(t):{}:n?eh({element:n,padding:r}).fn(t):{}}}),eE=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:i,y:o,placement:a,middlewareData:s}=t,l=await j(t,e);return a===(null==(n=s.offset)?void 0:n.placement)&&null!=(r=s.arrow)&&r.alignmentOffset?{}:{x:i+l.x,y:o+l.y,data:{...l,placement:a}}}}}(e),options:[e,t]}),eC=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:i}=t,{mainAxis:o=!0,crossAxis:a=!1,limiter:s={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...l}=w(e,t),u={x:n,y:r},c=await D(t,l),d=R(x(i)),f=C(d),m=u[f],v=u[d];if(o){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=m+c[e],r=m-c[t];m=p(n,h(m,r))}if(a){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=v+c[e],r=v-c[t];v=p(n,h(v,r))}let y=s.fn({...t,[f]:m,[d]:v});return{...y,data:{x:y.x-n,y:y.y-r,enabled:{[f]:o,[d]:a}}}}}}(e),options:[e,t]}),eT=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:i,rects:o,middlewareData:a}=t,{offset:s=0,mainAxis:l=!0,crossAxis:u=!0}=w(e,t),c={x:n,y:r},d=R(i),f=C(d),h=c[f],p=c[d],m=w(s,t),v="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(l){let e="y"===f?"height":"width",t=o.reference[f]-o.floating[e]+v.mainAxis,n=o.reference[f]+o.reference[e]-v.mainAxis;h<t?h=t:h>n&&(h=n)}if(u){var y,g;let e="y"===f?"width":"height",t=["top","left"].includes(x(i)),n=o.reference[d]-o.floating[e]+(t&&(null==(y=a.offset)?void 0:y[d])||0)+(t?0:v.crossAxis),r=o.reference[d]+o.reference[e]+(t?0:(null==(g=a.offset)?void 0:g[d])||0)-(t?v.crossAxis:0);p<n?p=n:p>r&&(p=r)}return{[f]:h,[d]:p}}}}(e),options:[e,t]}),eR=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,i,o,a;let{placement:s,middlewareData:l,rects:u,initialPlacement:c,platform:d,elements:f}=t,{mainAxis:h=!0,crossAxis:p=!0,fallbackPlacements:m,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:y="none",flipAlignment:g=!0,...b}=w(e,t);if(null!=(n=l.arrow)&&n.alignmentOffset)return{};let O=x(s),N=R(c),A=x(c)===c,k=await (null==d.isRTL?void 0:d.isRTL(f.floating)),M=m||(A||!g?[S(c)]:function(e){let t=S(e);return[P(e),t,P(t)]}(c)),L="none"!==y;!m&&L&&M.push(...function(e,t,n,r){let i=E(e),o=function(e,t,n){let r=["left","right"],i=["right","left"];switch(e){case"top":case"bottom":if(n)return t?i:r;return t?r:i;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(x(e),"start"===n,r);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(P)))),o}(c,g,y,k));let j=[c,...M],F=await D(t,b),I=[],q=(null==(r=l.flip)?void 0:r.overflows)||[];if(h&&I.push(F[O]),p){let e=function(e,t,n){void 0===n&&(n=!1);let r=E(e),i=C(R(e)),o=T(i),a="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[o]>t.floating[o]&&(a=S(a)),[a,S(a)]}(s,u,k);I.push(F[e[0]],F[e[1]])}if(q=[...q,{placement:s,overflows:I}],!I.every(e=>e<=0)){let e=((null==(i=l.flip)?void 0:i.index)||0)+1,t=j[e];if(t)return{data:{index:e,overflows:q},reset:{placement:t}};let n=null==(o=q.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(v){case"bestFit":{let e=null==(a=q.filter(e=>{if(L){let t=R(e.placement);return t===N||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=c}if(s!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eP=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let i,o,{placement:a,rects:s,platform:l,elements:u}=t,{apply:c=()=>{},...d}=w(e,t),f=await D(t,d),m=x(a),v=E(a),y="y"===R(a),{width:g,height:b}=s.floating;"top"===m||"bottom"===m?(i=m,o=v===(await (null==l.isRTL?void 0:l.isRTL(u.floating))?"start":"end")?"left":"right"):(o=m,i="end"===v?"top":"bottom");let C=b-f.top-f.bottom,T=g-f.left-f.right,P=h(b-f[i],C),S=h(g-f[o],T),O=!t.middlewareData.shift,N=P,A=S;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(A=T),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(N=C),O&&!v){let e=p(f.left,0),t=p(f.right,0),n=p(f.top,0),r=p(f.bottom,0);y?A=g-2*(0!==e||0!==t?e+t:p(f.left,f.right)):N=b-2*(0!==n||0!==r?n+r:p(f.top,f.bottom))}await c({...t,availableWidth:A,availableHeight:N});let k=await l.getDimensions(u.floating);return g!==k.width||b!==k.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eS=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...i}=w(e,t);switch(r){case"referenceHidden":{let e=M(await D(t,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:L(e)}}}case"escaped":{let e=M(await D(t,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:L(e)}}}default:return{}}}}}(e),options:[e,t]}),eO=(e,t)=>({...ex(e),options:[e,t]});var eN=n(3655),eA=n(5155),ek=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:i=5,...o}=e;return(0,eA.jsx)(eN.sG.svg,{...o,ref:t,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eA.jsx)("polygon",{points:"0,0 30,0 15,10"})})});ek.displayName="Arrow";var eD=n(9033),eM="Popper",[eL,ej]=function(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let i=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:i}}),[n,i])}};return i.scopeName=e,[function(t,i){let o=r.createContext(i),a=n.length;function s(t){let{scope:n,children:i,...s}=t,l=n?.[e][a]||o,u=r.useMemo(()=>s,Object.values(s));return(0,eA.jsx)(l.Provider,{value:u,children:i})}return n=[...n,i],s.displayName=t+"Provider",[s,function(n,s){let l=s?.[e][a]||o,u=r.useContext(l);if(u)return u;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=n.reduce((t,{useScope:n,scopeName:r})=>{let i=n(e)[`__scope${r}`];return{...t,...i}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}(i,...t)]}(eM),[eF,eI]=eL(eM),eq=e=>{let{__scopePopper:t,children:n}=e,[i,o]=r.useState(null);return(0,eA.jsx)(eF,{scope:t,anchor:i,onAnchorChange:o,children:n})};eq.displayName=eM;var eB="PopperAnchor",eH=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:i,...o}=e,s=eI(eB,n),l=r.useRef(null),u=(0,a.s)(t,l);return r.useEffect(()=>{s.onAnchorChange((null==i?void 0:i.current)||l.current)}),i?null:(0,eA.jsx)(eN.sG.div,{...o,ref:u})});eH.displayName=eB;var e_="PopperContent",[ez,eU]=eL(e_),eW=r.forwardRef((e,t)=>{var n,i,o,s,l,c,d,f;let{__scopePopper:m,side:y="bottom",sideOffset:g=0,align:b="center",alignOffset:w=0,arrowPadding:x=0,avoidCollisions:E=!0,collisionBoundary:C=[],collisionPadding:T=0,sticky:R="partial",hideWhenDetached:P=!1,updatePositionStrategy:S="optimized",onPlaced:O,...N}=e,A=eI(e_,m),[k,D]=r.useState(null),M=(0,a.s)(t,e=>D(e)),[L,j]=r.useState(null),F=function(e){let[t,n]=r.useState(void 0);return(0,u.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,i;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,i=t.blockSize}else r=e.offsetWidth,i=e.offsetHeight;n({width:r,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(L),I=null!=(d=null==F?void 0:F.width)?d:0,q=null!=(f=null==F?void 0:F.height)?f:0,H="number"==typeof T?T:{top:0,right:0,bottom:0,left:0,...T},_=Array.isArray(C)?C:[C],z=_.length>0,U={padding:H,boundary:_.filter(eV),altBoundary:z},{refs:W,floatingStyles:K,placement:Q,isPositioned:G,middlewareData:V}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:o,elements:{reference:a,floating:s}={},transform:l=!0,whileElementsMounted:u,open:c}=e,[d,f]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,p]=r.useState(i);ey(h,i)||p(i);let[m,v]=r.useState(null),[y,g]=r.useState(null),b=r.useCallback(e=>{e!==C.current&&(C.current=e,v(e))},[]),w=r.useCallback(e=>{e!==T.current&&(T.current=e,g(e))},[]),x=a||m,E=s||y,C=r.useRef(null),T=r.useRef(null),R=r.useRef(d),P=null!=u,S=ew(u),O=ew(o),N=ew(c),A=r.useCallback(()=>{if(!C.current||!T.current)return;let e={placement:t,strategy:n,middleware:h};O.current&&(e.platform=O.current),ep(C.current,T.current,e).then(e=>{let t={...e,isPositioned:!1!==N.current};k.current&&!ey(R.current,t)&&(R.current=t,em.flushSync(()=>{f(t)}))})},[h,t,n,O,N]);ev(()=>{!1===c&&R.current.isPositioned&&(R.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[c]);let k=r.useRef(!1);ev(()=>(k.current=!0,()=>{k.current=!1}),[]),ev(()=>{if(x&&(C.current=x),E&&(T.current=E),x&&E){if(S.current)return S.current(x,E,A);A()}},[x,E,A,S,P]);let D=r.useMemo(()=>({reference:C,floating:T,setReference:b,setFloating:w}),[b,w]),M=r.useMemo(()=>({reference:x,floating:E}),[x,E]),L=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!M.floating)return e;let t=eb(M.floating,d.x),r=eb(M.floating,d.y);return l?{...e,transform:"translate("+t+"px, "+r+"px)",...eg(M.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,l,M.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:A,refs:D,elements:M,floatingStyles:L}),[d,A,D,M,L])}({strategy:"fixed",placement:y+("center"!==b?"-"+b:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let i;void 0===r&&(r={});let{ancestorScroll:o=!0,ancestorResize:a=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:l="function"==typeof IntersectionObserver,animationFrame:u=!1}=r,c=et(e),d=o||a?[...c?Z(c):[],...Z(t)]:[];d.forEach(e=>{o&&e.addEventListener("scroll",n,{passive:!0}),a&&e.addEventListener("resize",n)});let f=c&&l?function(e,t){let n,r=null,i=B(e);function o(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function a(s,l){void 0===s&&(s=!1),void 0===l&&(l=1),o();let{left:u,top:c,width:d,height:f}=e.getBoundingClientRect();if(s||t(),!d||!f)return;let m=v(c),y=v(i.clientWidth-(u+d)),g={rootMargin:-m+"px "+-y+"px "+-v(i.clientHeight-(c+f))+"px "+-v(u)+"px",threshold:p(0,h(1,l))||1},b=!0;function w(e){let t=e[0].intersectionRatio;if(t!==l){if(!b)return a();t?a(!1,t):n=setTimeout(()=>{a(!1,1e-7)},1e3)}b=!1}try{r=new IntersectionObserver(w,{...g,root:i.ownerDocument})}catch(e){r=new IntersectionObserver(w,g)}r.observe(e)}(!0),o}(c,n):null,m=-1,y=null;s&&(y=new ResizeObserver(e=>{let[r]=e;r&&r.target===c&&y&&(y.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var e;null==(e=y)||e.observe(t)})),n()}),c&&!u&&y.observe(c),y.observe(t));let g=u?eo(e):null;return u&&function t(){let r=eo(e);g&&(r.x!==g.x||r.y!==g.y||r.width!==g.width||r.height!==g.height)&&n(),g=r,i=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{o&&e.removeEventListener("scroll",n),a&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=y)||e.disconnect(),y=null,u&&cancelAnimationFrame(i)}}(...t,{animationFrame:"always"===S})},elements:{reference:A.anchor},middleware:[eE({mainAxis:g+q,alignmentAxis:w}),E&&eC({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?eT():void 0,...U}),E&&eR({...U}),eP({...U,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:i}=e,{width:o,height:a}=n.reference,s=t.floating.style;s.setProperty("--radix-popper-available-width","".concat(r,"px")),s.setProperty("--radix-popper-available-height","".concat(i,"px")),s.setProperty("--radix-popper-anchor-width","".concat(o,"px")),s.setProperty("--radix-popper-anchor-height","".concat(a,"px"))}}),L&&eO({element:L,padding:x}),eY({arrowWidth:I,arrowHeight:q}),P&&eS({strategy:"referenceHidden",...U})]}),[Y,$]=e$(Q),X=(0,eD.c)(O);(0,u.N)(()=>{G&&(null==X||X())},[G,X]);let J=null==(n=V.arrow)?void 0:n.x,ee=null==(i=V.arrow)?void 0:i.y,en=(null==(o=V.arrow)?void 0:o.centerOffset)!==0,[er,ei]=r.useState();return(0,u.N)(()=>{k&&ei(window.getComputedStyle(k).zIndex)},[k]),(0,eA.jsx)("div",{ref:W.setFloating,"data-radix-popper-content-wrapper":"",style:{...K,transform:G?K.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:er,"--radix-popper-transform-origin":[null==(s=V.transformOrigin)?void 0:s.x,null==(l=V.transformOrigin)?void 0:l.y].join(" "),...(null==(c=V.hide)?void 0:c.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eA.jsx)(ez,{scope:m,placedSide:Y,onArrowChange:j,arrowX:J,arrowY:ee,shouldHideArrow:en,children:(0,eA.jsx)(eN.sG.div,{"data-side":Y,"data-align":$,...N,ref:M,style:{...N.style,animation:G?void 0:"none"}})})})});eW.displayName=e_;var eK="PopperArrow",eQ={top:"bottom",right:"left",bottom:"top",left:"right"},eG=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,i=eU(eK,n),o=eQ[i.placedSide];return(0,eA.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,eA.jsx)(ek,{...r,ref:t,style:{...r.style,display:"block"}})})});function eV(e){return null!==e}eG.displayName=eK;var eY=e=>({name:"transformOrigin",options:e,fn(t){var n,r,i,o,a;let{placement:s,rects:l,middlewareData:u}=t,c=(null==(n=u.arrow)?void 0:n.centerOffset)!==0,d=c?0:e.arrowWidth,f=c?0:e.arrowHeight,[h,p]=e$(s),m={start:"0%",center:"50%",end:"100%"}[p],v=(null!=(o=null==(r=u.arrow)?void 0:r.x)?o:0)+d/2,y=(null!=(a=null==(i=u.arrow)?void 0:i.y)?a:0)+f/2,g="",b="";return"bottom"===h?(g=c?m:"".concat(v,"px"),b="".concat(-f,"px")):"top"===h?(g=c?m:"".concat(v,"px"),b="".concat(l.floating.height+f,"px")):"right"===h?(g="".concat(-f,"px"),b=c?m:"".concat(y,"px")):"left"===h&&(g="".concat(l.floating.width+f,"px"),b=c?m:"".concat(y,"px")),{data:{x:g,y:b}}}});function e$(e){let[t,n="center"]=e.split("-");return[t,n]}n(4378);var eX=n(8905),eZ=n(9708),eJ=n(5845),e0=n(2564),[e1,e5]=(0,s.A)("Tooltip",[ej]),e2=ej(),e3="TooltipProvider",e4="tooltip.open",[e7,e8]=e1(e3),e6=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:i=300,disableHoverableContent:o=!1,children:a}=e,[s,l]=r.useState(!0),u=r.useRef(!1),c=r.useRef(0);return r.useEffect(()=>{let e=c.current;return()=>window.clearTimeout(e)},[]),(0,eA.jsx)(e7,{scope:t,isOpenDelayed:s,delayDuration:n,onOpen:r.useCallback(()=>{window.clearTimeout(c.current),l(!1)},[]),onClose:r.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>l(!0),i)},[i]),isPointerInTransitRef:u,onPointerInTransitChange:r.useCallback(e=>{u.current=e},[]),disableHoverableContent:o,children:a})};e6.displayName=e3;var e9="Tooltip",[te,tt]=e1(e9),tn=e=>{let{__scopeTooltip:t,children:n,open:i,defaultOpen:o=!1,onOpenChange:a,disableHoverableContent:s,delayDuration:l}=e,f=e8(e9,e.__scopeTooltip),h=e2(t),[p,m]=r.useState(null),v=function(e){let[t,n]=r.useState(c());return(0,u.N)(()=>{n(e=>e??String(d++))},[void 0]),e||(t?`radix-${t}`:"")}(),y=r.useRef(0),g=null!=s?s:f.disableHoverableContent,b=null!=l?l:f.delayDuration,w=r.useRef(!1),[x=!1,E]=(0,eJ.i)({prop:i,defaultProp:o,onChange:e=>{e?(f.onOpen(),document.dispatchEvent(new CustomEvent(e4))):f.onClose(),null==a||a(e)}}),C=r.useMemo(()=>x?w.current?"delayed-open":"instant-open":"closed",[x]),T=r.useCallback(()=>{window.clearTimeout(y.current),y.current=0,w.current=!1,E(!0)},[E]),R=r.useCallback(()=>{window.clearTimeout(y.current),y.current=0,E(!1)},[E]),P=r.useCallback(()=>{window.clearTimeout(y.current),y.current=window.setTimeout(()=>{w.current=!0,E(!0),y.current=0},b)},[b,E]);return r.useEffect(()=>()=>{y.current&&(window.clearTimeout(y.current),y.current=0)},[]),(0,eA.jsx)(eq,{...h,children:(0,eA.jsx)(te,{scope:t,contentId:v,open:x,stateAttribute:C,trigger:p,onTriggerChange:m,onTriggerEnter:r.useCallback(()=>{f.isOpenDelayed?P():T()},[f.isOpenDelayed,P,T]),onTriggerLeave:r.useCallback(()=>{g?R():(window.clearTimeout(y.current),y.current=0)},[R,g]),onOpen:T,onClose:R,disableHoverableContent:g,children:n})})};tn.displayName=e9;var tr="TooltipTrigger",ti=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...i}=e,s=tt(tr,n),l=e8(tr,n),u=e2(n),c=r.useRef(null),d=(0,a.s)(t,c,s.onTriggerChange),f=r.useRef(!1),h=r.useRef(!1),p=r.useCallback(()=>f.current=!1,[]);return r.useEffect(()=>()=>document.removeEventListener("pointerup",p),[p]),(0,eA.jsx)(eH,{asChild:!0,...u,children:(0,eA.jsx)(eN.sG.button,{"aria-describedby":s.open?s.contentId:void 0,"data-state":s.stateAttribute,...i,ref:d,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(h.current||l.isPointerInTransitRef.current||(s.onTriggerEnter(),h.current=!0))}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{s.onTriggerLeave(),h.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{f.current=!0,document.addEventListener("pointerup",p,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{f.current||s.onOpen()}),onBlur:(0,o.m)(e.onBlur,s.onClose),onClick:(0,o.m)(e.onClick,s.onClose)})})});ti.displayName=tr;var[to,ta]=e1("TooltipPortal",{forceMount:void 0}),ts="TooltipContent",tl=r.forwardRef((e,t)=>{let n=ta(ts,e.__scopeTooltip),{forceMount:r=n.forceMount,side:i="top",...o}=e,a=tt(ts,e.__scopeTooltip);return(0,eA.jsx)(eX.C,{present:r||a.open,children:a.disableHoverableContent?(0,eA.jsx)(tf,{side:i,...o,ref:t}):(0,eA.jsx)(tu,{side:i,...o,ref:t})})}),tu=r.forwardRef((e,t)=>{let n=tt(ts,e.__scopeTooltip),i=e8(ts,e.__scopeTooltip),o=r.useRef(null),s=(0,a.s)(t,o),[l,u]=r.useState(null),{trigger:c,onClose:d}=n,f=o.current,{onPointerInTransitChange:h}=i,p=r.useCallback(()=>{u(null),h(!1)},[h]),m=r.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},i=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),i=Math.abs(t.right-e.x),o=Math.abs(t.left-e.x);switch(Math.min(n,r,i,o)){case o:return"left";case i:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,i),...function(e){let{top:t,right:n,bottom:r,left:i}=e;return[{x:i,y:t},{x:n,y:t},{x:n,y:r},{x:i,y:r}]}(t.getBoundingClientRect())])),h(!0)},[h]);return r.useEffect(()=>()=>p(),[p]),r.useEffect(()=>{if(c&&f){let e=e=>m(e,f),t=e=>m(e,c);return c.addEventListener("pointerleave",e),f.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),f.removeEventListener("pointerleave",t)}}},[c,f,m,p]),r.useEffect(()=>{if(l){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=(null==c?void 0:c.contains(t))||(null==f?void 0:f.contains(t)),i=!function(e,t){let{x:n,y:r}=e,i=!1;for(let e=0,o=t.length-1;e<t.length;o=e++){let a=t[e].x,s=t[e].y,l=t[o].x,u=t[o].y;s>r!=u>r&&n<(l-a)*(r-s)/(u-s)+a&&(i=!i)}return i}(n,l);r?p():i&&(p(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,f,l,d,p]),(0,eA.jsx)(tf,{...e,ref:s})}),[tc,td]=e1(e9,{isInside:!1}),tf=r.forwardRef((e,t)=>{let{__scopeTooltip:n,children:i,"aria-label":o,onEscapeKeyDown:a,onPointerDownOutside:s,...u}=e,c=tt(ts,n),d=e2(n),{onClose:f}=c;return r.useEffect(()=>(document.addEventListener(e4,f),()=>document.removeEventListener(e4,f)),[f]),r.useEffect(()=>{if(c.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(c.trigger))&&f()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[c.trigger,f]),(0,eA.jsx)(l.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:a,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:f,children:(0,eA.jsxs)(eW,{"data-state":c.stateAttribute,...d,...u,ref:t,style:{...u.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,eA.jsx)(eZ.xV,{children:i}),(0,eA.jsx)(tc,{scope:n,isInside:!0,children:(0,eA.jsx)(e0.b,{id:c.contentId,role:"tooltip",children:o||i})})]})})});tl.displayName=ts;var th="TooltipArrow";r.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,i=e2(n);return td(th,n).isInside?null:(0,eA.jsx)(eG,{...i,...r,ref:t})}).displayName=th;var tp=e6,tm=tn,tv=ti,ty=tl},1362:(e,t,n)=>{"use strict";n.d(t,{useTheme:()=>s});var r=n(2115),i="(prefers-color-scheme: dark)",o=r.createContext(void 0),a={setTheme:e=>{},themes:[]},s=()=>{var e;return null!=(e=r.useContext(o))?e:a},l=null,u=(e,t)=>{let n;try{n=localStorage.getItem(e)||void 0}catch(e){}return n||t},c=()=>{let e=document.createElement("style");return e.appendChild(document.createTextNode("*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(e),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(e)},1)}},d=e=>(e||(e=window.matchMedia(i)),e.matches?"dark":"light")},2423:(e,t,n)=>{"use strict";n.d(t,{Action:()=>et,Close:()=>en,Description:()=>ee,Provider:()=>$,Root:()=>Z,Title:()=>J,Viewport:()=>X});var r=n(2115),i=n(7650),o=n(5185),a=n(6101),s=n(5155),l=n(9708),u=n(6081),c=n(9178),d=n(4378),f=n(8905),h=n(3655),p=n(9033),m=n(5845),v=n(2712),y=n(2564),g="ToastProvider",[b,w,x]=function(e){let t=e+"CollectionProvider",[n,i]=function(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let i=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:i}}),[n,i])}};return i.scopeName=e,[function(t,i){let o=r.createContext(i),a=n.length;function l(t){let{scope:n,children:i,...l}=t,u=n?.[e][a]||o,c=r.useMemo(()=>l,Object.values(l));return(0,s.jsx)(u.Provider,{value:c,children:i})}return n=[...n,i],l.displayName=t+"Provider",[l,function(n,s){let l=s?.[e][a]||o,u=r.useContext(l);if(u)return u;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=n.reduce((t,{useScope:n,scopeName:r})=>{let i=n(e)[`__scope${r}`];return{...t,...i}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}(i,...t)]}(t),[o,u]=n(t,{collectionRef:{current:null},itemMap:new Map}),c=e=>{let{scope:t,children:n}=e,i=r.useRef(null),a=r.useRef(new Map).current;return(0,s.jsx)(o,{scope:t,itemMap:a,collectionRef:i,children:n})};c.displayName=t;let d=e+"CollectionSlot",f=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,i=u(d,n),o=(0,a.s)(t,i.collectionRef);return(0,s.jsx)(l.DX,{ref:o,children:r})});f.displayName=d;let h=e+"CollectionItemSlot",p="data-radix-collection-item",m=r.forwardRef((e,t)=>{let{scope:n,children:i,...o}=e,c=r.useRef(null),d=(0,a.s)(t,c),f=u(h,n);return r.useEffect(()=>(f.itemMap.set(c,{ref:c,...o}),()=>void f.itemMap.delete(c))),(0,s.jsx)(l.DX,{...{[p]:""},ref:d,children:i})});return m.displayName=h,[{Provider:c,Slot:f,ItemSlot:m},function(t){let n=u(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(p,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},i]}("Toast"),[E,C]=(0,u.A)("Toast",[x]),[T,R]=E(g),P=e=>{let{__scopeToast:t,label:n="Notification",duration:i=5e3,swipeDirection:o="right",swipeThreshold:a=50,children:l}=e,[u,c]=r.useState(null),[d,f]=r.useState(0),h=r.useRef(!1),p=r.useRef(!1);return n.trim()||console.error("Invalid prop `label` supplied to `".concat(g,"`. Expected non-empty `string`.")),(0,s.jsx)(b.Provider,{scope:t,children:(0,s.jsx)(T,{scope:t,label:n,duration:i,swipeDirection:o,swipeThreshold:a,toastCount:d,viewport:u,onViewportChange:c,onToastAdd:r.useCallback(()=>f(e=>e+1),[]),onToastRemove:r.useCallback(()=>f(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:h,isClosePausedRef:p,children:l})})};P.displayName=g;var S="ToastViewport",O=["F8"],N="toast.viewportPause",A="toast.viewportResume",k=r.forwardRef((e,t)=>{let{__scopeToast:n,hotkey:i=O,label:o="Notifications ({hotkey})",...l}=e,u=R(S,n),d=w(n),f=r.useRef(null),p=r.useRef(null),m=r.useRef(null),v=r.useRef(null),y=(0,a.s)(t,v,u.onViewportChange),g=i.join("+").replace(/Key/g,"").replace(/Digit/g,""),x=u.toastCount>0;r.useEffect(()=>{let e=e=>{var t;0!==i.length&&i.every(t=>e[t]||e.code===t)&&(null==(t=v.current)||t.focus())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[i]),r.useEffect(()=>{let e=f.current,t=v.current;if(x&&e&&t){let n=()=>{if(!u.isClosePausedRef.current){let e=new CustomEvent(N);t.dispatchEvent(e),u.isClosePausedRef.current=!0}},r=()=>{if(u.isClosePausedRef.current){let e=new CustomEvent(A);t.dispatchEvent(e),u.isClosePausedRef.current=!1}},i=t=>{e.contains(t.relatedTarget)||r()},o=()=>{e.contains(document.activeElement)||r()};return e.addEventListener("focusin",n),e.addEventListener("focusout",i),e.addEventListener("pointermove",n),e.addEventListener("pointerleave",o),window.addEventListener("blur",n),window.addEventListener("focus",r),()=>{e.removeEventListener("focusin",n),e.removeEventListener("focusout",i),e.removeEventListener("pointermove",n),e.removeEventListener("pointerleave",o),window.removeEventListener("blur",n),window.removeEventListener("focus",r)}}},[x,u.isClosePausedRef]);let E=r.useCallback(e=>{let{tabbingDirection:t}=e,n=d().map(e=>{let n=e.ref.current,r=[n,...function(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}(n)];return"forwards"===t?r:r.reverse()});return("forwards"===t?n.reverse():n).flat()},[d]);return r.useEffect(()=>{let e=v.current;if(e){let t=t=>{let n=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!n){var r,i,o;let n=document.activeElement,a=t.shiftKey;if(t.target===e&&a){null==(r=p.current)||r.focus();return}let s=E({tabbingDirection:a?"backwards":"forwards"}),l=s.findIndex(e=>e===n);Y(s.slice(l+1))?t.preventDefault():a?null==(i=p.current)||i.focus():null==(o=m.current)||o.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[d,E]),(0,s.jsxs)(c.lg,{ref:f,role:"region","aria-label":o.replace("{hotkey}",g),tabIndex:-1,style:{pointerEvents:x?void 0:"none"},children:[x&&(0,s.jsx)(M,{ref:p,onFocusFromOutsideViewport:()=>{Y(E({tabbingDirection:"forwards"}))}}),(0,s.jsx)(b.Slot,{scope:n,children:(0,s.jsx)(h.sG.ol,{tabIndex:-1,...l,ref:y})}),x&&(0,s.jsx)(M,{ref:m,onFocusFromOutsideViewport:()=>{Y(E({tabbingDirection:"backwards"}))}})]})});k.displayName=S;var D="ToastFocusProxy",M=r.forwardRef((e,t)=>{let{__scopeToast:n,onFocusFromOutsideViewport:r,...i}=e,o=R(D,n);return(0,s.jsx)(y.s,{"aria-hidden":!0,tabIndex:0,...i,ref:t,style:{position:"fixed"},onFocus:e=>{var t;let n=e.relatedTarget;(null==(t=o.viewport)?void 0:t.contains(n))||r()}})});M.displayName=D;var L="Toast",j=r.forwardRef((e,t)=>{let{forceMount:n,open:r,defaultOpen:i,onOpenChange:a,...l}=e,[u=!0,c]=(0,m.i)({prop:r,defaultProp:i,onChange:a});return(0,s.jsx)(f.C,{present:n||u,children:(0,s.jsx)(q,{open:u,...l,ref:t,onClose:()=>c(!1),onPause:(0,p.c)(e.onPause),onResume:(0,p.c)(e.onResume),onSwipeStart:(0,o.m)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,o.m)(e.onSwipeMove,e=>{let{x:t,y:n}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y","".concat(n,"px"))}),onSwipeCancel:(0,o.m)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,o.m)(e.onSwipeEnd,e=>{let{x:t,y:n}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y","".concat(n,"px")),c(!1)})})})});j.displayName=L;var[F,I]=E(L,{onClose(){}}),q=r.forwardRef((e,t)=>{let{__scopeToast:n,type:l="foreground",duration:u,open:d,onClose:f,onEscapeKeyDown:m,onPause:v,onResume:y,onSwipeStart:g,onSwipeMove:w,onSwipeCancel:x,onSwipeEnd:E,...C}=e,T=R(L,n),[P,S]=r.useState(null),O=(0,a.s)(t,e=>S(e)),k=r.useRef(null),D=r.useRef(null),M=u||T.duration,j=r.useRef(0),I=r.useRef(M),q=r.useRef(0),{onToastAdd:H,onToastRemove:_}=T,z=(0,p.c)(()=>{var e;(null==P?void 0:P.contains(document.activeElement))&&(null==(e=T.viewport)||e.focus()),f()}),U=r.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(q.current),j.current=new Date().getTime(),q.current=window.setTimeout(z,e))},[z]);r.useEffect(()=>{let e=T.viewport;if(e){let t=()=>{U(I.current),null==y||y()},n=()=>{let e=new Date().getTime()-j.current;I.current=I.current-e,window.clearTimeout(q.current),null==v||v()};return e.addEventListener(N,n),e.addEventListener(A,t),()=>{e.removeEventListener(N,n),e.removeEventListener(A,t)}}},[T.viewport,M,v,y,U]),r.useEffect(()=>{d&&!T.isClosePausedRef.current&&U(M)},[d,M,T.isClosePausedRef,U]),r.useEffect(()=>(H(),()=>_()),[H,_]);let W=r.useMemo(()=>P?function e(t){let n=[];return Array.from(t.childNodes).forEach(t=>{var r;if(t.nodeType===t.TEXT_NODE&&t.textContent&&n.push(t.textContent),(r=t).nodeType===r.ELEMENT_NODE){let r=t.ariaHidden||t.hidden||"none"===t.style.display,i=""===t.dataset.radixToastAnnounceExclude;if(!r)if(i){let e=t.dataset.radixToastAnnounceAlt;e&&n.push(e)}else n.push(...e(t))}}),n}(P):null,[P]);return T.viewport?(0,s.jsxs)(s.Fragment,{children:[W&&(0,s.jsx)(B,{__scopeToast:n,role:"status","aria-live":"foreground"===l?"assertive":"polite","aria-atomic":!0,children:W}),(0,s.jsx)(F,{scope:n,onClose:z,children:i.createPortal((0,s.jsx)(b.ItemSlot,{scope:n,children:(0,s.jsx)(c.bL,{asChild:!0,onEscapeKeyDown:(0,o.m)(m,()=>{T.isFocusedToastEscapeKeyDownRef.current||z(),T.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,s.jsx)(h.sG.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":d?"open":"closed","data-swipe-direction":T.swipeDirection,...C,ref:O,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,o.m)(e.onKeyDown,e=>{"Escape"===e.key&&(null==m||m(e.nativeEvent),e.nativeEvent.defaultPrevented||(T.isFocusedToastEscapeKeyDownRef.current=!0,z()))}),onPointerDown:(0,o.m)(e.onPointerDown,e=>{0===e.button&&(k.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,o.m)(e.onPointerMove,e=>{if(!k.current)return;let t=e.clientX-k.current.x,n=e.clientY-k.current.y,r=!!D.current,i=["left","right"].includes(T.swipeDirection),o=["left","up"].includes(T.swipeDirection)?Math.min:Math.max,a=i?o(0,t):0,s=i?0:o(0,n),l="touch"===e.pointerType?10:2,u={x:a,y:s},c={originalEvent:e,delta:u};r?(D.current=u,G("toast.swipeMove",w,c,{discrete:!1})):V(u,T.swipeDirection,l)?(D.current=u,G("toast.swipeStart",g,c,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>l||Math.abs(n)>l)&&(k.current=null)}),onPointerUp:(0,o.m)(e.onPointerUp,e=>{let t=D.current,n=e.target;if(n.hasPointerCapture(e.pointerId)&&n.releasePointerCapture(e.pointerId),D.current=null,k.current=null,t){let n=e.currentTarget,r={originalEvent:e,delta:t};V(t,T.swipeDirection,T.swipeThreshold)?G("toast.swipeEnd",E,r,{discrete:!0}):G("toast.swipeCancel",x,r,{discrete:!0}),n.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),T.viewport)})]}):null}),B=e=>{let{__scopeToast:t,children:n,...i}=e,o=R(L,t),[a,l]=r.useState(!1),[u,c]=r.useState(!1);return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{},t=(0,p.c)(e);(0,v.N)(()=>{let e=0,n=0;return e=window.requestAnimationFrame(()=>n=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(n)}},[t])}(()=>l(!0)),r.useEffect(()=>{let e=window.setTimeout(()=>c(!0),1e3);return()=>window.clearTimeout(e)},[]),u?null:(0,s.jsx)(d.Z,{asChild:!0,children:(0,s.jsx)(y.s,{...i,children:a&&(0,s.jsxs)(s.Fragment,{children:[o.label," ",n]})})})},H=r.forwardRef((e,t)=>{let{__scopeToast:n,...r}=e;return(0,s.jsx)(h.sG.div,{...r,ref:t})});H.displayName="ToastTitle";var _=r.forwardRef((e,t)=>{let{__scopeToast:n,...r}=e;return(0,s.jsx)(h.sG.div,{...r,ref:t})});_.displayName="ToastDescription";var z="ToastAction",U=r.forwardRef((e,t)=>{let{altText:n,...r}=e;return n.trim()?(0,s.jsx)(Q,{altText:n,asChild:!0,children:(0,s.jsx)(K,{...r,ref:t})}):(console.error("Invalid prop `altText` supplied to `".concat(z,"`. Expected non-empty `string`.")),null)});U.displayName=z;var W="ToastClose",K=r.forwardRef((e,t)=>{let{__scopeToast:n,...r}=e,i=I(W,n);return(0,s.jsx)(Q,{asChild:!0,children:(0,s.jsx)(h.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,i.onClose)})})});K.displayName=W;var Q=r.forwardRef((e,t)=>{let{__scopeToast:n,altText:r,...i}=e;return(0,s.jsx)(h.sG.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...i,ref:t})});function G(e,t,n,r){let{discrete:i}=r,o=n.originalEvent.currentTarget,a=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),i?(0,h.hO)(o,a):o.dispatchEvent(a)}var V=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=Math.abs(e.x),i=Math.abs(e.y),o=r>i;return"left"===t||"right"===t?o&&r>n:!o&&i>n};function Y(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var $=P,X=k,Z=j,J=H,ee=_,et=U,en=K},2564:(e,t,n)=>{"use strict";n.d(t,{b:()=>s,s:()=>a});var r=n(2115),i=n(3655),o=n(5155),a=r.forwardRef((e,t)=>(0,o.jsx)(i.sG.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));a.displayName="VisuallyHidden";var s=a},2712:(e,t,n)=>{"use strict";n.d(t,{N:()=>i});var r=n(2115),i=globalThis?.document?r.useLayoutEffect:()=>{}},2922:(e,t,n)=>{"use strict";n.d(t,{E:()=>j});var r="undefined"==typeof window||"Deno"in globalThis;function i(){}function o(e,t){return"function"==typeof e?e(t):e}function a(e,t){let{type:n="all",exact:r,fetchStatus:i,predicate:o,queryKey:a,stale:s}=e;if(a){if(r){if(t.queryHash!==l(a,t.options))return!1}else if(!c(t.queryKey,a))return!1}if("all"!==n){let e=t.isActive();if("active"===n&&!e||"inactive"===n&&e)return!1}return("boolean"!=typeof s||t.isStale()===s)&&(!i||i===t.state.fetchStatus)&&(!o||!!o(t))}function s(e,t){let{exact:n,status:r,predicate:i,mutationKey:o}=e;if(o){if(!t.options.mutationKey)return!1;if(n){if(u(t.options.mutationKey)!==u(o))return!1}else if(!c(t.options.mutationKey,o))return!1}return(!r||t.state.status===r)&&(!i||!!i(t))}function l(e,t){return(t?.queryKeyHashFn||u)(e)}function u(e){return JSON.stringify(e,(e,t)=>f(t)?Object.keys(t).sort().reduce((e,n)=>(e[n]=t[n],e),{}):t)}function c(e,t){return e===t||typeof e==typeof t&&!!e&&!!t&&"object"==typeof e&&"object"==typeof t&&!Object.keys(t).some(n=>!c(e[n],t[n]))}function d(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function f(e){if(!h(e))return!1;let t=e.constructor;if(void 0===t)return!0;let n=t.prototype;return!!h(n)&&!!n.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(e)===Object.prototype}function h(e){return"[object Object]"===Object.prototype.toString.call(e)}function p(e,t,n=0){let r=[...e,t];return n&&r.length>n?r.slice(1):r}function m(e,t,n=0){let r=[t,...e];return n&&r.length>n?r.slice(0,-1):r}var v=Symbol();function y(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:e.queryFn&&e.queryFn!==v?e.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${e.queryHash}'`))}var g=function(){let e=[],t=0,n=e=>{e()},r=e=>{e()},i=e=>setTimeout(e,0),o=r=>{t?e.push(r):i(()=>{n(r)})},a=()=>{let t=e;e=[],t.length&&i(()=>{r(()=>{t.forEach(e=>{n(e)})})})};return{batch:e=>{let n;t++;try{n=e()}finally{--t||a()}return n},batchCalls:e=>(...t)=>{o(()=>{e(...t)})},schedule:o,setNotifyFunction:e=>{n=e},setBatchNotifyFunction:e=>{r=e},setScheduler:e=>{i=e}}}(),b=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},w=new class extends b{#e;#t;#n;constructor(){super(),this.#n=e=>{if(!r&&window.addEventListener){let t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}}}onSubscribe(){this.#t||this.setEventListener(this.#n)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#n=e,this.#t?.(),this.#t=e(e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()})}setFocused(e){this.#e!==e&&(this.#e=e,this.onFocus())}onFocus(){let e=this.isFocused();this.listeners.forEach(t=>{t(e)})}isFocused(){return"boolean"==typeof this.#e?this.#e:globalThis.document?.visibilityState!=="hidden"}},x=new class extends b{#r=!0;#t;#n;constructor(){super(),this.#n=e=>{if(!r&&window.addEventListener){let t=()=>e(!0),n=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",n,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",n)}}}}onSubscribe(){this.#t||this.setEventListener(this.#n)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#n=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){this.#r!==e&&(this.#r=e,this.listeners.forEach(t=>{t(e)}))}isOnline(){return this.#r}};function E(e){return Math.min(1e3*2**e,3e4)}function C(e){return(e??"online")!=="online"||x.isOnline()}var T=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function R(e){return e instanceof T}function P(e){let t,n=!1,i=0,o=!1,a=function(){let e,t,n=new Promise((n,r)=>{e=n,t=r});function r(e){Object.assign(n,e),delete n.resolve,delete n.reject}return n.status="pending",n.catch(()=>{}),n.resolve=t=>{r({status:"fulfilled",value:t}),e(t)},n.reject=e=>{r({status:"rejected",reason:e}),t(e)},n}(),s=()=>w.isFocused()&&("always"===e.networkMode||x.isOnline())&&e.canRun(),l=()=>C(e.networkMode)&&e.canRun(),u=n=>{o||(o=!0,e.onSuccess?.(n),t?.(),a.resolve(n))},c=n=>{o||(o=!0,e.onError?.(n),t?.(),a.reject(n))},d=()=>new Promise(n=>{t=e=>{(o||s())&&n(e)},e.onPause?.()}).then(()=>{t=void 0,o||e.onContinue?.()}),f=()=>{let t;if(o)return;let a=0===i?e.initialPromise:void 0;try{t=a??e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(u).catch(t=>{if(o)return;let a=e.retry??3*!r,l=e.retryDelay??E,u="function"==typeof l?l(i,t):l,h=!0===a||"number"==typeof a&&i<a||"function"==typeof a&&a(i,t);if(n||!h)return void c(t);i++,e.onFail?.(i,t),new Promise(e=>{setTimeout(e,u)}).then(()=>s()?void 0:d()).then(()=>{n?c(t):f()})})};return{promise:a,cancel:t=>{o||(c(new T(t)),e.abort?.())},continue:()=>(t?.(),a),cancelRetry:()=>{n=!0},continueRetry:()=>{n=!1},canStart:l,start:()=>(l()?f():d().then(f),a)}}var S=class{#i;destroy(){this.clearGcTimeout()}scheduleGc(){var e;this.clearGcTimeout(),"number"==typeof(e=this.gcTime)&&e>=0&&e!==1/0&&(this.#i=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(r?1/0:3e5))}clearGcTimeout(){this.#i&&(clearTimeout(this.#i),this.#i=void 0)}},O=class extends S{#o;#a;#s;#l;#u;#c;constructor(e){super(),this.#c=!1,this.#u=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#s=e.cache,this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#o=function(e){let t="function"==typeof e.initialData?e.initialData():e.initialData,n=void 0!==t,r=n?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:n?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:n?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#o,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#l?.promise}setOptions(e){this.options={...this.#u,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#s.remove(this)}setData(e,t){var n,r;let i=(n=this.state.data,"function"==typeof(r=this.options).structuralSharing?r.structuralSharing(n,e):!1!==r.structuralSharing?function e(t,n){if(t===n)return t;let r=d(t)&&d(n);if(r||f(t)&&f(n)){let i=r?t:Object.keys(t),o=i.length,a=r?n:Object.keys(n),s=a.length,l=r?[]:{},u=0;for(let o=0;o<s;o++){let s=r?o:a[o];(!r&&i.includes(s)||r)&&void 0===t[s]&&void 0===n[s]?(l[s]=void 0,u++):(l[s]=e(t[s],n[s]),l[s]===t[s]&&void 0!==t[s]&&u++)}return o===s&&u===o?t:l}return n}(n,e):e);return this.#d({data:i,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),i}setState(e,t){this.#d({type:"setState",state:e,setStateOptions:t})}cancel(e){let t=this.#l?.promise;return this.#l?.cancel(e),t?t.then(i).catch(i):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#o)}isActive(){return this.observers.some(e=>{var t;return!1!==(t=e.options.enabled,"function"==typeof t?t(this):t)})}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===v||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some(e=>e.getCurrentResult().isStale):void 0===this.state.data)}isStaleByTime(e=0){return this.state.isInvalidated||void 0===this.state.data||!Math.max(this.state.dataUpdatedAt+(e||0)-Date.now(),0)}onFocus(){let e=this.observers.find(e=>e.shouldFetchOnWindowFocus());e?.refetch({cancelRefetch:!1}),this.#l?.continue()}onOnline(){let e=this.observers.find(e=>e.shouldFetchOnReconnect());e?.refetch({cancelRefetch:!1}),this.#l?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#s.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(t=>t!==e),this.observers.length||(this.#l&&(this.#c?this.#l.cancel({revert:!0}):this.#l.cancelRetry()),this.scheduleGc()),this.#s.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#d({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus){if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#l)return this.#l.continueRetry(),this.#l.promise}if(e&&this.setOptions(e),!this.options.queryFn){let e=this.observers.find(e=>e.options.queryFn);e&&this.setOptions(e.options)}let n=new AbortController,r=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#c=!0,n.signal)})},i={fetchOptions:t,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:()=>{let e=y(this.options,t),n={queryKey:this.queryKey,meta:this.meta};return(r(n),this.#c=!1,this.options.persister)?this.options.persister(e,n,this):e(n)}};r(i),this.options.behavior?.onFetch(i,this),this.#a=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==i.fetchOptions?.meta)&&this.#d({type:"fetch",meta:i.fetchOptions?.meta});let o=e=>{R(e)&&e.silent||this.#d({type:"error",error:e}),R(e)||(this.#s.config.onError?.(e,this),this.#s.config.onSettled?.(this.state.data,e,this)),this.scheduleGc()};return this.#l=P({initialPromise:t?.initialPromise,fn:i.fetchFn,abort:n.abort.bind(n),onSuccess:e=>{if(void 0===e)return void o(Error(`${this.queryHash} data is undefined`));try{this.setData(e)}catch(e){o(e);return}this.#s.config.onSuccess?.(e,this),this.#s.config.onSettled?.(e,this.state.error,this),this.scheduleGc()},onError:o,onFail:(e,t)=>{this.#d({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#d({type:"pause"})},onContinue:()=>{this.#d({type:"continue"})},retry:i.options.retry,retryDelay:i.options.retryDelay,networkMode:i.options.networkMode,canRun:()=>!0}),this.#l.start()}#d(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":var n;return{...t,...(n=t.data,{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:C(this.options.networkMode)?"fetching":"paused",...void 0===n&&{error:null,status:"pending"}}),fetchMeta:e.meta??null};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let r=e.error;if(R(r)&&r.revert&&this.#a)return{...this.#a,fetchStatus:"idle"};return{...t,error:r,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:r,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),g.batch(()=>{this.observers.forEach(e=>{e.onQueryUpdate()}),this.#s.notify({query:this,type:"updated",action:e})})}},N=class extends b{constructor(e={}){super(),this.config=e,this.#f=new Map}#f;build(e,t,n){let r=t.queryKey,i=t.queryHash??l(r,t),o=this.get(i);return o||(o=new O({cache:this,queryKey:r,queryHash:i,options:e.defaultQueryOptions(t),state:n,defaultOptions:e.getQueryDefaults(r)}),this.add(o)),o}add(e){this.#f.has(e.queryHash)||(this.#f.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let t=this.#f.get(e.queryHash);t&&(e.destroy(),t===e&&this.#f.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){g.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#f.get(e)}getAll(){return[...this.#f.values()]}find(e){let t={exact:!0,...e};return this.getAll().find(e=>a(t,e))}findAll(e={}){let t=this.getAll();return Object.keys(e).length>0?t.filter(t=>a(e,t)):t}notify(e){g.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){g.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){g.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},A=class extends S{#h;#p;#l;constructor(e){super(),this.mutationId=e.mutationId,this.#p=e.mutationCache,this.#h=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#h.includes(e)||(this.#h.push(e),this.clearGcTimeout(),this.#p.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#h=this.#h.filter(t=>t!==e),this.scheduleGc(),this.#p.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#h.length||("pending"===this.state.status?this.scheduleGc():this.#p.remove(this))}continue(){return this.#l?.continue()??this.execute(this.state.variables)}async execute(e){this.#l=P({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#d({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#d({type:"pause"})},onContinue:()=>{this.#d({type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#p.canRun(this)});let t="pending"===this.state.status,n=!this.#l.canStart();try{if(!t){this.#d({type:"pending",variables:e,isPaused:n}),await this.#p.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#d({type:"pending",context:t,variables:e,isPaused:n})}let r=await this.#l.start();return await this.#p.config.onSuccess?.(r,e,this.state.context,this),await this.options.onSuccess?.(r,e,this.state.context),await this.#p.config.onSettled?.(r,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(r,null,e,this.state.context),this.#d({type:"success",data:r}),r}catch(t){try{throw await this.#p.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#p.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#d({type:"error",error:t})}}finally{this.#p.runNext(this)}}#d(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),g.batch(()=>{this.#h.forEach(t=>{t.onMutationUpdate(e)}),this.#p.notify({mutation:this,type:"updated",action:e})})}},k=class extends b{constructor(e={}){super(),this.config=e,this.#m=new Map,this.#v=Date.now()}#m;#v;build(e,t,n){let r=new A({mutationCache:this,mutationId:++this.#v,options:e.defaultMutationOptions(t),state:n});return this.add(r),r}add(e){let t=D(e),n=this.#m.get(t)??[];n.push(e),this.#m.set(t,n),this.notify({type:"added",mutation:e})}remove(e){let t=D(e);if(this.#m.has(t)){let n=this.#m.get(t)?.filter(t=>t!==e);n&&(0===n.length?this.#m.delete(t):this.#m.set(t,n))}this.notify({type:"removed",mutation:e})}canRun(e){let t=this.#m.get(D(e))?.find(e=>"pending"===e.state.status);return!t||t===e}runNext(e){let t=this.#m.get(D(e))?.find(t=>t!==e&&t.state.isPaused);return t?.continue()??Promise.resolve()}clear(){g.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}getAll(){return[...this.#m.values()].flat()}find(e){let t={exact:!0,...e};return this.getAll().find(e=>s(t,e))}findAll(e={}){return this.getAll().filter(t=>s(e,t))}notify(e){g.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return g.batch(()=>Promise.all(e.map(e=>e.continue().catch(i))))}};function D(e){return e.options.scope?.id??String(e.mutationId)}function M(e){return{onFetch:(t,n)=>{let r=t.options,i=t.fetchOptions?.meta?.fetchMore?.direction,o=t.state.data?.pages||[],a=t.state.data?.pageParams||[],s={pages:[],pageParams:[]},l=0,u=async()=>{let n=!1,u=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(t.signal.aborted?n=!0:t.signal.addEventListener("abort",()=>{n=!0}),t.signal)})},c=y(t.options,t.fetchOptions),d=async(e,r,i)=>{if(n)return Promise.reject();if(null==r&&e.pages.length)return Promise.resolve(e);let o={queryKey:t.queryKey,pageParam:r,direction:i?"backward":"forward",meta:t.options.meta};u(o);let a=await c(o),{maxPages:s}=t.options,l=i?m:p;return{pages:l(e.pages,a,s),pageParams:l(e.pageParams,r,s)}};if(i&&o.length){let e="backward"===i,t={pages:o,pageParams:a},n=(e?function(e,{pages:t,pageParams:n}){return t.length>0?e.getPreviousPageParam?.(t[0],t,n[0],n):void 0}:L)(r,t);s=await d(t,n,e)}else{let t=e??o.length;do{let e=0===l?a[0]??r.initialPageParam:L(r,s);if(l>0&&null==e)break;s=await d(s,e),l++}while(l<t)}return s};t.options.persister?t.fetchFn=()=>t.options.persister?.(u,{queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},n):t.fetchFn=u}}}function L(e,{pages:t,pageParams:n}){let r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,n[r],n):void 0}var j=class{#y;#p;#u;#g;#b;#w;#x;#E;constructor(e={}){this.#y=e.queryCache||new N,this.#p=e.mutationCache||new k,this.#u=e.defaultOptions||{},this.#g=new Map,this.#b=new Map,this.#w=0}mount(){this.#w++,1===this.#w&&(this.#x=w.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#y.onFocus())}),this.#E=x.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#y.onOnline())}))}unmount(){this.#w--,0===this.#w&&(this.#x?.(),this.#x=void 0,this.#E?.(),this.#E=void 0)}isFetching(e){return this.#y.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#p.findAll({...e,status:"pending"}).length}getQueryData(e){let t=this.defaultQueryOptions({queryKey:e});return this.#y.get(t.queryHash)?.state.data}ensureQueryData(e){let t=this.getQueryData(e.queryKey);if(void 0===t)return this.fetchQuery(e);{let n=this.defaultQueryOptions(e),r=this.#y.build(this,n);return e.revalidateIfStale&&r.isStaleByTime(o(n.staleTime,r))&&this.prefetchQuery(n),Promise.resolve(t)}}getQueriesData(e){return this.#y.findAll(e).map(({queryKey:e,state:t})=>[e,t.data])}setQueryData(e,t,n){let r=this.defaultQueryOptions({queryKey:e}),i=this.#y.get(r.queryHash),o=i?.state.data,a="function"==typeof t?t(o):t;if(void 0!==a)return this.#y.build(this,r).setData(a,{...n,manual:!0})}setQueriesData(e,t,n){return g.batch(()=>this.#y.findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,n)]))}getQueryState(e){let t=this.defaultQueryOptions({queryKey:e});return this.#y.get(t.queryHash)?.state}removeQueries(e){let t=this.#y;g.batch(()=>{t.findAll(e).forEach(e=>{t.remove(e)})})}resetQueries(e,t){let n=this.#y,r={type:"active",...e};return g.batch(()=>(n.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries(r,t)))}cancelQueries(e={},t={}){let n={revert:!0,...t};return Promise.all(g.batch(()=>this.#y.findAll(e).map(e=>e.cancel(n)))).then(i).catch(i)}invalidateQueries(e={},t={}){return g.batch(()=>{if(this.#y.findAll(e).forEach(e=>{e.invalidate()}),"none"===e.refetchType)return Promise.resolve();let n={...e,type:e.refetchType??e.type??"active"};return this.refetchQueries(n,t)})}refetchQueries(e={},t){let n={...t,cancelRefetch:t?.cancelRefetch??!0};return Promise.all(g.batch(()=>this.#y.findAll(e).filter(e=>!e.isDisabled()).map(e=>{let t=e.fetch(void 0,n);return n.throwOnError||(t=t.catch(i)),"paused"===e.state.fetchStatus?Promise.resolve():t}))).then(i)}fetchQuery(e){let t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);let n=this.#y.build(this,t);return n.isStaleByTime(o(t.staleTime,n))?n.fetch(t):Promise.resolve(n.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(i).catch(i)}fetchInfiniteQuery(e){return e.behavior=M(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(i).catch(i)}ensureInfiniteQueryData(e){return e.behavior=M(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return x.isOnline()?this.#p.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#y}getMutationCache(){return this.#p}getDefaultOptions(){return this.#u}setDefaultOptions(e){this.#u=e}setQueryDefaults(e,t){this.#g.set(u(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){let t=[...this.#g.values()],n={};return t.forEach(t=>{c(e,t.queryKey)&&(n={...n,...t.defaultOptions})}),n}setMutationDefaults(e,t){this.#b.set(u(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){let t=[...this.#b.values()],n={};return t.forEach(t=>{c(e,t.mutationKey)&&(n={...n,...t.defaultOptions})}),n}defaultQueryOptions(e){if(e._defaulted)return e;let t={...this.#u.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=l(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),!0!==t.enabled&&t.queryFn===v&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#u.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#y.clear(),this.#p.clear()}}},3655:(e,t,n)=>{"use strict";n.d(t,{hO:()=>l,sG:()=>s});var r=n(2115),i=n(7650),o=n(9708),a=n(5155),s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=r.forwardRef((e,n)=>{let{asChild:r,...i}=e,s=r?o.DX:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(s,{...i,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function l(e,t){e&&i.flushSync(()=>e.dispatchEvent(t))}},4378:(e,t,n)=>{"use strict";n.d(t,{Z:()=>l});var r=n(2115),i=n(7650),o=n(3655),a=n(2712),s=n(5155),l=r.forwardRef((e,t)=>{var n,l;let{container:u,...c}=e,[d,f]=r.useState(!1);(0,a.N)(()=>f(!0),[]);let h=u||d&&(null==(l=globalThis)||null==(n=l.document)?void 0:n.body);return h?i.createPortal((0,s.jsx)(o.sG.div,{...c,ref:t}),h):null});l.displayName="Portal"},5185:(e,t,n)=>{"use strict";function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}n.d(t,{m:()=>r})},5845:(e,t,n)=>{"use strict";n.d(t,{i:()=>o});var r=n(2115),i=n(9033);function o({prop:e,defaultProp:t,onChange:n=()=>{}}){let[o,a]=function({defaultProp:e,onChange:t}){let n=r.useState(e),[o]=n,a=r.useRef(o),s=(0,i.c)(t);return r.useEffect(()=>{a.current!==o&&(s(o),a.current=o)},[o,a,s]),n}({defaultProp:t,onChange:n}),s=void 0!==e,l=s?e:o,u=(0,i.c)(n);return[l,r.useCallback(t=>{if(s){let n="function"==typeof t?t(e):t;n!==e&&u(n)}else a(t)},[s,e,a,u])]}},6081:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(2115),i=n(5155);function o(e,t=[]){let n=[],a=()=>{let t=n.map(e=>r.createContext(e));return function(n){let i=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:i}}),[n,i])}};return a.scopeName=e,[function(t,o){let a=r.createContext(o),s=n.length;n=[...n,o];let l=t=>{let{scope:n,children:o,...l}=t,u=n?.[e]?.[s]||a,c=r.useMemo(()=>l,Object.values(l));return(0,i.jsx)(u.Provider,{value:c,children:o})};return l.displayName=t+"Provider",[l,function(n,i){let l=i?.[e]?.[s]||a,u=r.useContext(l);if(u)return u;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=n.reduce((t,{useScope:n,scopeName:r})=>{let i=n(e)[`__scope${r}`];return{...t,...i}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}(a,...t)]}},6101:(e,t,n)=>{"use strict";n.d(t,{s:()=>o,t:()=>i});var r=n(2115);function i(...e){return t=>e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})}function o(...e){return r.useCallback(i(...e),e)}},6671:(e,t,n)=>{"use strict";n.d(t,{Toaster:()=>x,toast:()=>v});var r=n(2115),i=n(7650),o=e=>{switch(e){case"success":return l;case"info":return c;case"warning":return u;case"error":return d;default:return null}},a=Array(12).fill(0),s=e=>{let{visible:t}=e;return r.createElement("div",{className:"sonner-loading-wrapper","data-visible":t},r.createElement("div",{className:"sonner-spinner"},a.map((e,t)=>r.createElement("div",{className:"sonner-loading-bar",key:"spinner-bar-".concat(t)}))))},l=r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},r.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),u=r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},r.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),c=r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},r.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),d=r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},r.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),f=()=>{let[e,t]=r.useState(document.hidden);return r.useEffect(()=>{let e=()=>{t(document.hidden)};return document.addEventListener("visibilitychange",e),()=>window.removeEventListener("visibilitychange",e)},[]),e},h=1,p=new class{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach(t=>t(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let{message:n,...r}=e,i="number"==typeof(null==e?void 0:e.id)||(null==(t=e.id)?void 0:t.length)>0?e.id:h++,o=this.toasts.find(e=>e.id===i),a=void 0===e.dismissible||e.dismissible;return o?this.toasts=this.toasts.map(t=>t.id===i?(this.publish({...t,...e,id:i,title:n}),{...t,...e,id:i,dismissible:a,title:n}):t):this.addToast({title:n,...r,dismissible:a,id:i}),i},this.dismiss=e=>(e||this.toasts.forEach(e=>{this.subscribers.forEach(t=>t({id:e.id,dismiss:!0}))}),this.subscribers.forEach(t=>t({id:e,dismiss:!0})),e),this.message=(e,t)=>this.create({...t,message:e}),this.error=(e,t)=>this.create({...t,message:e,type:"error"}),this.success=(e,t)=>this.create({...t,type:"success",message:e}),this.info=(e,t)=>this.create({...t,type:"info",message:e}),this.warning=(e,t)=>this.create({...t,type:"warning",message:e}),this.loading=(e,t)=>this.create({...t,type:"loading",message:e}),this.promise=(e,t)=>{let n;if(!t)return;void 0!==t.loading&&(n=this.create({...t,promise:e,type:"loading",message:t.loading,description:"function"!=typeof t.description?t.description:void 0}));let r=e instanceof Promise?e:e(),i=void 0!==n;return r.then(async e=>{if(m(e)&&!e.ok){i=!1;let r="function"==typeof t.error?await t.error("HTTP error! status: ".concat(e.status)):t.error,o="function"==typeof t.description?await t.description("HTTP error! status: ".concat(e.status)):t.description;this.create({id:n,type:"error",message:r,description:o})}else if(void 0!==t.success){i=!1;let r="function"==typeof t.success?await t.success(e):t.success,o="function"==typeof t.description?await t.description(e):t.description;this.create({id:n,type:"success",message:r,description:o})}}).catch(async e=>{if(void 0!==t.error){i=!1;let r="function"==typeof t.error?await t.error(e):t.error,o="function"==typeof t.description?await t.description(e):t.description;this.create({id:n,type:"error",message:r,description:o})}}).finally(()=>{var e;i&&(this.dismiss(n),n=void 0),null==(e=t.finally)||e.call(t)}),n},this.custom=(e,t)=>{let n=(null==t?void 0:t.id)||h++;return this.create({jsx:e(n),id:n,...t}),n},this.subscribers=[],this.toasts=[]}},m=e=>e&&"object"==typeof e&&"ok"in e&&"boolean"==typeof e.ok&&"status"in e&&"number"==typeof e.status,v=Object.assign((e,t)=>{let n=(null==t?void 0:t.id)||h++;return p.addToast({title:e,...t,id:n}),n},{success:p.success,info:p.info,warning:p.warning,error:p.error,custom:p.custom,message:p.message,promise:p.promise,dismiss:p.dismiss,loading:p.loading},{getHistory:()=>p.toasts});function y(e){return void 0!==e.label}function g(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter(Boolean).join(" ")}!function(e){let{insertAt:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!e||"undefined"==typeof document)return;let n=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css","top"===t&&n.firstChild?n.insertBefore(r,n.firstChild):n.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}(':where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999}:where([data-sonner-toaster][data-x-position="right"]){right:max(var(--offset),env(safe-area-inset-right))}:where([data-sonner-toaster][data-x-position="left"]){left:max(var(--offset),env(safe-area-inset-left))}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:max(var(--offset),env(safe-area-inset-top))}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:max(var(--offset),env(safe-area-inset-bottom))}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;background:var(--gray1);color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:0;right:0;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount, 0px));transition:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation:swipe-out .2s ease-out forwards}@keyframes swipe-out{0%{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount)));opacity:1}to{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount) + var(--lift) * -100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;--mobile-offset: 16px;right:var(--mobile-offset);left:var(--mobile-offset);width:100%}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset)}[data-sonner-toaster][data-y-position=bottom]{bottom:20px}[data-sonner-toaster][data-y-position=top]{top:20px}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset);right:var(--mobile-offset);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}\n');var b=e=>{var t,n,i,a,l,u,c,d,h,p;let{invert:m,toast:v,unstyled:g,interacting:b,setHeights:w,visibleToasts:x,heights:E,index:C,toasts:T,expanded:R,removeToast:P,defaultRichColors:S,closeButton:O,style:N,cancelButtonStyle:A,actionButtonStyle:k,className:D="",descriptionClassName:M="",duration:L,position:j,gap:F,loadingIcon:I,expandByDefault:q,classNames:B,icons:H,closeButtonAriaLabel:_="Close toast",pauseWhenPageIsHidden:z,cn:U}=e,[W,K]=r.useState(!1),[Q,G]=r.useState(!1),[V,Y]=r.useState(!1),[$,X]=r.useState(!1),[Z,J]=r.useState(0),[ee,et]=r.useState(0),en=r.useRef(null),er=r.useRef(null),ei=0===C,eo=C+1<=x,ea=v.type,es=!1!==v.dismissible,el=v.className||"",eu=v.descriptionClassName||"",ec=r.useMemo(()=>E.findIndex(e=>e.toastId===v.id)||0,[E,v.id]),ed=r.useMemo(()=>{var e;return null!=(e=v.closeButton)?e:O},[v.closeButton,O]),ef=r.useMemo(()=>v.duration||L||4e3,[v.duration,L]),eh=r.useRef(0),ep=r.useRef(0),em=r.useRef(0),ev=r.useRef(null),[ey,eg]=j.split("-"),eb=r.useMemo(()=>E.reduce((e,t,n)=>n>=ec?e:e+t.height,0),[E,ec]),ew=f(),ex=v.invert||m,eE="loading"===ea;ep.current=r.useMemo(()=>ec*F+eb,[ec,eb]),r.useEffect(()=>{K(!0)},[]),r.useLayoutEffect(()=>{if(!W)return;let e=er.current,t=e.style.height;e.style.height="auto";let n=e.getBoundingClientRect().height;e.style.height=t,et(n),w(e=>e.find(e=>e.toastId===v.id)?e.map(e=>e.toastId===v.id?{...e,height:n}:e):[{toastId:v.id,height:n,position:v.position},...e])},[W,v.title,v.description,w,v.id]);let eC=r.useCallback(()=>{G(!0),J(ep.current),w(e=>e.filter(e=>e.toastId!==v.id)),setTimeout(()=>{P(v)},200)},[v,P,w,ep]);return r.useEffect(()=>{if(v.promise&&"loading"===ea||v.duration===1/0||"loading"===v.type)return;let e,t=ef;return R||b||z&&ew?(()=>{if(em.current<eh.current){let e=new Date().getTime()-eh.current;t-=e}em.current=new Date().getTime()})():t!==1/0&&(eh.current=new Date().getTime(),e=setTimeout(()=>{var e;null==(e=v.onAutoClose)||e.call(v,v),eC()},t)),()=>clearTimeout(e)},[R,b,q,v,ef,eC,v.promise,ea,z,ew]),r.useEffect(()=>{let e=er.current;if(e){let t=e.getBoundingClientRect().height;return et(t),w(e=>[{toastId:v.id,height:t,position:v.position},...e]),()=>w(e=>e.filter(e=>e.toastId!==v.id))}},[w,v.id]),r.useEffect(()=>{v.delete&&eC()},[eC,v.delete]),r.createElement("li",{"aria-live":v.important?"assertive":"polite","aria-atomic":"true",role:"status",tabIndex:0,ref:er,className:U(D,el,null==B?void 0:B.toast,null==(t=null==v?void 0:v.classNames)?void 0:t.toast,null==B?void 0:B.default,null==B?void 0:B[ea],null==(n=null==v?void 0:v.classNames)?void 0:n[ea]),"data-sonner-toast":"","data-rich-colors":null!=(i=v.richColors)?i:S,"data-styled":!(v.jsx||v.unstyled||g),"data-mounted":W,"data-promise":!!v.promise,"data-removed":Q,"data-visible":eo,"data-y-position":ey,"data-x-position":eg,"data-index":C,"data-front":ei,"data-swiping":V,"data-dismissible":es,"data-type":ea,"data-invert":ex,"data-swipe-out":$,"data-expanded":!!(R||q&&W),style:{"--index":C,"--toasts-before":C,"--z-index":T.length-C,"--offset":"".concat(Q?Z:ep.current,"px"),"--initial-height":q?"auto":"".concat(ee,"px"),...N,...v.style},onPointerDown:e=>{eE||!es||(en.current=new Date,J(ep.current),e.target.setPointerCapture(e.pointerId),"BUTTON"!==e.target.tagName&&(Y(!0),ev.current={x:e.clientX,y:e.clientY}))},onPointerUp:()=>{var e,t,n,r;if($||!es)return;ev.current=null;let i=Number((null==(e=er.current)?void 0:e.style.getPropertyValue("--swipe-amount").replace("px",""))||0),o=Math.abs(i)/(new Date().getTime()-(null==(t=en.current)?void 0:t.getTime()));if(Math.abs(i)>=20||o>.11){J(ep.current),null==(n=v.onDismiss)||n.call(v,v),eC(),X(!0);return}null==(r=er.current)||r.style.setProperty("--swipe-amount","0px"),Y(!1)},onPointerMove:e=>{var t;if(!ev.current||!es)return;let n=e.clientY-ev.current.y,r=e.clientX-ev.current.x,i=("top"===ey?Math.min:Math.max)(0,n),o="touch"===e.pointerType?10:2;Math.abs(i)>o?null==(t=er.current)||t.style.setProperty("--swipe-amount","".concat(n,"px")):Math.abs(r)>o&&(ev.current=null)}},ed&&!v.jsx?r.createElement("button",{"aria-label":_,"data-disabled":eE,"data-close-button":!0,onClick:eE||!es?()=>{}:()=>{var e;eC(),null==(e=v.onDismiss)||e.call(v,v)},className:U(null==B?void 0:B.closeButton,null==(a=null==v?void 0:v.classNames)?void 0:a.closeButton)},r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},r.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),r.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"}))):null,v.jsx||r.isValidElement(v.title)?v.jsx||v.title:r.createElement(r.Fragment,null,ea||v.icon||v.promise?r.createElement("div",{"data-icon":"",className:U(null==B?void 0:B.icon,null==(l=null==v?void 0:v.classNames)?void 0:l.icon)},v.promise||"loading"===v.type&&!v.icon?v.icon||(null!=H&&H.loading?r.createElement("div",{className:"sonner-loader","data-visible":"loading"===ea},H.loading):I?r.createElement("div",{className:"sonner-loader","data-visible":"loading"===ea},I):r.createElement(s,{visible:"loading"===ea})):null,"loading"!==v.type?v.icon||(null==H?void 0:H[ea])||o(ea):null):null,r.createElement("div",{"data-content":"",className:U(null==B?void 0:B.content,null==(u=null==v?void 0:v.classNames)?void 0:u.content)},r.createElement("div",{"data-title":"",className:U(null==B?void 0:B.title,null==(c=null==v?void 0:v.classNames)?void 0:c.title)},v.title),v.description?r.createElement("div",{"data-description":"",className:U(M,eu,null==B?void 0:B.description,null==(d=null==v?void 0:v.classNames)?void 0:d.description)},v.description):null),r.isValidElement(v.cancel)?v.cancel:v.cancel&&y(v.cancel)?r.createElement("button",{"data-button":!0,"data-cancel":!0,style:v.cancelButtonStyle||A,onClick:e=>{var t,n;y(v.cancel)&&es&&(null==(n=(t=v.cancel).onClick)||n.call(t,e),eC())},className:U(null==B?void 0:B.cancelButton,null==(h=null==v?void 0:v.classNames)?void 0:h.cancelButton)},v.cancel.label):null,r.isValidElement(v.action)?v.action:v.action&&y(v.action)?r.createElement("button",{"data-button":!0,"data-action":!0,style:v.actionButtonStyle||k,onClick:e=>{var t,n;y(v.action)&&(e.defaultPrevented||(null==(n=(t=v.action).onClick)||n.call(t,e),eC()))},className:U(null==B?void 0:B.actionButton,null==(p=null==v?void 0:v.classNames)?void 0:p.actionButton)},v.action.label):null))};function w(){if("undefined"==typeof window||"undefined"==typeof document)return"ltr";let e=document.documentElement.getAttribute("dir");return"auto"!==e&&e?e:window.getComputedStyle(document.documentElement).direction}var x=e=>{let{invert:t,position:n="bottom-right",hotkey:o=["altKey","KeyT"],expand:a,closeButton:s,className:l,offset:u,theme:c="light",richColors:d,duration:f,style:h,visibleToasts:m=3,toastOptions:v,dir:y=w(),gap:x=14,loadingIcon:E,icons:C,containerAriaLabel:T="Notifications",pauseWhenPageIsHidden:R,cn:P=g}=e,[S,O]=r.useState([]),N=r.useMemo(()=>Array.from(new Set([n].concat(S.filter(e=>e.position).map(e=>e.position)))),[S,n]),[A,k]=r.useState([]),[D,M]=r.useState(!1),[L,j]=r.useState(!1),[F,I]=r.useState("system"!==c?c:"undefined"!=typeof window&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),q=r.useRef(null),B=o.join("+").replace(/Key/g,"").replace(/Digit/g,""),H=r.useRef(null),_=r.useRef(!1),z=r.useCallback(e=>{var t;null!=(t=S.find(t=>t.id===e.id))&&t.delete||p.dismiss(e.id),O(t=>t.filter(t=>{let{id:n}=t;return n!==e.id}))},[S]);return r.useEffect(()=>p.subscribe(e=>{if(e.dismiss)return void O(t=>t.map(t=>t.id===e.id?{...t,delete:!0}:t));setTimeout(()=>{i.flushSync(()=>{O(t=>{let n=t.findIndex(t=>t.id===e.id);return -1!==n?[...t.slice(0,n),{...t[n],...e},...t.slice(n+1)]:[e,...t]})})})}),[]),r.useEffect(()=>{if("system"!==c)return void I(c);"system"===c&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?I("dark"):I("light")),"undefined"!=typeof window&&window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",e=>{let{matches:t}=e;I(t?"dark":"light")})},[c]),r.useEffect(()=>{S.length<=1&&M(!1)},[S]),r.useEffect(()=>{let e=e=>{var t,n;o.every(t=>e[t]||e.code===t)&&(M(!0),null==(t=q.current)||t.focus()),"Escape"===e.code&&(document.activeElement===q.current||null!=(n=q.current)&&n.contains(document.activeElement))&&M(!1)};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[o]),r.useEffect(()=>{if(q.current)return()=>{H.current&&(H.current.focus({preventScroll:!0}),H.current=null,_.current=!1)}},[q.current]),S.length?r.createElement("section",{"aria-label":"".concat(T," ").concat(B),tabIndex:-1},N.map((e,n)=>{var i;let[o,c]=e.split("-");return r.createElement("ol",{key:e,dir:"auto"===y?w():y,tabIndex:-1,ref:q,className:l,"data-sonner-toaster":!0,"data-theme":F,"data-y-position":o,"data-x-position":c,style:{"--front-toast-height":"".concat((null==(i=A[0])?void 0:i.height)||0,"px"),"--offset":"number"==typeof u?"".concat(u,"px"):u||"32px","--width":"".concat(356,"px"),"--gap":"".concat(x,"px"),...h},onBlur:e=>{_.current&&!e.currentTarget.contains(e.relatedTarget)&&(_.current=!1,H.current&&(H.current.focus({preventScroll:!0}),H.current=null))},onFocus:e=>{e.target instanceof HTMLElement&&"false"===e.target.dataset.dismissible||_.current||(_.current=!0,H.current=e.relatedTarget)},onMouseEnter:()=>M(!0),onMouseMove:()=>M(!0),onMouseLeave:()=>{L||M(!1)},onPointerDown:e=>{e.target instanceof HTMLElement&&"false"===e.target.dataset.dismissible||j(!0)},onPointerUp:()=>j(!1)},S.filter(t=>!t.position&&0===n||t.position===e).map((n,i)=>{var o,l;return r.createElement(b,{key:n.id,icons:C,index:i,toast:n,defaultRichColors:d,duration:null!=(o=null==v?void 0:v.duration)?o:f,className:null==v?void 0:v.className,descriptionClassName:null==v?void 0:v.descriptionClassName,invert:t,visibleToasts:m,closeButton:null!=(l=null==v?void 0:v.closeButton)?l:s,interacting:L,position:e,style:null==v?void 0:v.style,unstyled:null==v?void 0:v.unstyled,classNames:null==v?void 0:v.classNames,cancelButtonStyle:null==v?void 0:v.cancelButtonStyle,actionButtonStyle:null==v?void 0:v.actionButtonStyle,removeToast:z,toasts:S.filter(e=>e.position==n.position),heights:A.filter(e=>e.position==n.position),setHeights:k,expandByDefault:a,gap:x,loadingIcon:E,expanded:D,pauseWhenPageIsHidden:R,cn:P})}))})):null}},6715:(e,t,n)=>{"use strict";n.d(t,{Ht:()=>a});var r=n(2115),i=n(5155),o=r.createContext(void 0),a=e=>{let{client:t,children:n}=e;return r.useEffect(()=>(t.mount(),()=>{t.unmount()}),[t]),(0,i.jsx)(o.Provider,{value:t,children:n})}},8905:(e,t,n)=>{"use strict";n.d(t,{C:()=>a});var r=n(2115),i=n(6101),o=n(2712),a=e=>{let{present:t,children:n}=e,a=function(e){var t,n;let[i,a]=r.useState(),l=r.useRef({}),u=r.useRef(e),c=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=s(l.current);c.current="mounted"===d?e:"none"},[d]),(0,o.N)(()=>{let t=l.current,n=u.current;if(n!==e){let r=c.current,i=s(t);e?f("MOUNT"):"none"===i||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==i?f("ANIMATION_OUT"):f("UNMOUNT"),u.current=e}},[e,f]),(0,o.N)(()=>{if(i){var e;let t,n=null!=(e=i.ownerDocument.defaultView)?e:window,r=e=>{let r=s(l.current).includes(e.animationName);if(e.target===i&&r&&(f("ANIMATION_END"),!u.current)){let e=i.style.animationFillMode;i.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=e)})}},o=e=>{e.target===i&&(c.current=s(l.current))};return i.addEventListener("animationstart",o),i.addEventListener("animationcancel",r),i.addEventListener("animationend",r),()=>{n.clearTimeout(t),i.removeEventListener("animationstart",o),i.removeEventListener("animationcancel",r),i.removeEventListener("animationend",r)}}f("ANIMATION_END")},[i,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{e&&(l.current=getComputedStyle(e)),a(e)},[])}}(t),l="function"==typeof n?n({present:a.isPresent}):r.Children.only(n),u=(0,i.s)(a.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,i=r&&"isReactWarning"in r&&r.isReactWarning;return i?e.ref:(i=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof n||a.isPresent?r.cloneElement(l,{ref:u}):null};function s(e){return(null==e?void 0:e.animationName)||"none"}a.displayName="Presence"},9033:(e,t,n)=>{"use strict";n.d(t,{c:()=>i});var r=n(2115);function i(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},9178:(e,t,n)=>{"use strict";n.d(t,{lg:()=>y,qW:()=>f,bL:()=>v});var r,i=n(2115),o=n(5185),a=n(3655),s=n(6101),l=n(9033),u=n(5155),c="dismissableLayer.update",d=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=i.forwardRef((e,t)=>{var n,f;let{disableOutsidePointerEvents:h=!1,onEscapeKeyDown:v,onPointerDownOutside:y,onFocusOutside:g,onInteractOutside:b,onDismiss:w,...x}=e,E=i.useContext(d),[C,T]=i.useState(null),R=null!=(f=null==C?void 0:C.ownerDocument)?f:null==(n=globalThis)?void 0:n.document,[,P]=i.useState({}),S=(0,s.s)(t,e=>T(e)),O=Array.from(E.layers),[N]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),A=O.indexOf(N),k=C?O.indexOf(C):-1,D=E.layersWithOutsidePointerEventsDisabled.size>0,M=k>=A,L=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,l.c)(e),o=i.useRef(!1),a=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){m("dismissableLayer.pointerDownOutside",r,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",a.current),a.current=t,n.addEventListener("click",a.current,{once:!0})):t()}else n.removeEventListener("click",a.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",a.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...E.branches].some(e=>e.contains(t));M&&!n&&(null==y||y(e),null==b||b(e),e.defaultPrevented||null==w||w())},R),j=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,l.c)(e),o=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!o.current&&m("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;![...E.branches].some(e=>e.contains(t))&&(null==g||g(e),null==b||b(e),e.defaultPrevented||null==w||w())},R);return!function(e,t=globalThis?.document){let n=(0,l.c)(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{k===E.layers.size-1&&(null==v||v(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},R),i.useEffect(()=>{if(C)return h&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(r=R.body.style.pointerEvents,R.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(C)),E.layers.add(C),p(),()=>{h&&1===E.layersWithOutsidePointerEventsDisabled.size&&(R.body.style.pointerEvents=r)}},[C,R,h,E]),i.useEffect(()=>()=>{C&&(E.layers.delete(C),E.layersWithOutsidePointerEventsDisabled.delete(C),p())},[C,E]),i.useEffect(()=>{let e=()=>P({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,u.jsx)(a.sG.div,{...x,ref:S,style:{pointerEvents:D?M?"auto":"none":void 0,...e.style},onFocusCapture:(0,o.m)(e.onFocusCapture,j.onFocusCapture),onBlurCapture:(0,o.m)(e.onBlurCapture,j.onBlurCapture),onPointerDownCapture:(0,o.m)(e.onPointerDownCapture,L.onPointerDownCapture)})});f.displayName="DismissableLayer";var h=i.forwardRef((e,t)=>{let n=i.useContext(d),r=i.useRef(null),o=(0,s.s)(t,r);return i.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,u.jsx)(a.sG.div,{...e,ref:o})});function p(){let e=new CustomEvent(c);document.dispatchEvent(e)}function m(e,t,n,r){let{discrete:i}=r,o=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),i?(0,a.hO)(o,s):o.dispatchEvent(s)}h.displayName="DismissableLayerBranch";var v=f,y=h},9708:(e,t,n)=>{"use strict";n.d(t,{DX:()=>a,xV:()=>l});var r=n(2115),i=n(6101),o=n(5155),a=r.forwardRef((e,t)=>{let{children:n,...i}=e,a=r.Children.toArray(n),l=a.find(u);if(l){let e=l.props.children,n=a.map(t=>t!==l?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,o.jsx)(s,{...i,ref:t,children:r.isValidElement(e)?r.cloneElement(e,void 0,n):null})}return(0,o.jsx)(s,{...i,ref:t,children:n})});a.displayName="Slot";var s=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(n);return r.cloneElement(n,{...function(e,t){let n={...t};for(let r in t){let i=e[r],o=t[r];/^on[A-Z]/.test(r)?i&&o?n[r]=(...e)=>{o(...e),i(...e)}:i&&(n[r]=i):"style"===r?n[r]={...i,...o}:"className"===r&&(n[r]=[i,o].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props),ref:t?(0,i.t)(t,e):e})}return r.Children.count(n)>1?r.Children.only(null):null});s.displayName="SlotClone";var l=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});function u(e){return r.isValidElement(e)&&e.type===l}},9840:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c"}}}]);