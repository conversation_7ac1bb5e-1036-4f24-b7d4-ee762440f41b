import { But<PERSON> } from "@/components/ui/button";
import { ArrowDown } from "lucide-react";

const Hero = () => {
  return (
    <section id="home" className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Gradient */}
      <div className="absolute inset-0 hero-gradient"></div>
      
      {/* Animated World Map Background */}
      <div className="absolute inset-0 dotted-world-map"></div>
      
      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="animate-fade-in-up">
          <h1 className="text-5xl sm:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight">
            Innovating Brands,
            <br />
            <span className="text-accent">Elevating Experiences.</span>
          </h1>
          
          <p className="text-xl sm:text-2xl text-white/90 mb-8 max-w-3xl mx-auto leading-relaxed">
            Web Design. Branding. Marketing. All in One. 
            <br />
            Intellinex is your global partner in creating powerful digital solutions that inspire and convert.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button 
              size="lg" 
              className="bg-white text-primary hover:bg-white/90 transition-all transform hover:scale-105 px-8 py-4 text-lg font-semibold"
            >
              Let's Build Together
            </Button>
            
            <Button 
              variant="outline" 
              size="lg"
              className="border-white text-white hover:bg-white hover:text-primary transition-all transform hover:scale-105 px-8 py-4 text-lg font-semibold"
            >
              View Our Work
            </Button>
          </div>
        </div>
        
        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <ArrowDown className="text-white/60 w-6 h-6" />
        </div>
      </div>
      
      {/* Trusted By Section */}
      <div className="absolute bottom-0 left-0 right-0 bg-white/10 backdrop-blur-sm border-t border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <p className="text-center text-white/80 text-sm font-medium">
            Trusted by Bold Brands Worldwide
          </p>
        </div>
      </div>
    </section>
  );
};

export default Hero;