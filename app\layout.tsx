import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { Toaster } from "@/components/ui/toaster"
import { Toaster as Sonner } from "@/components/ui/sonner"
import { TooltipProvider } from "@/components/ui/tooltip"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { ReactQueryProvider } from './providers'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Intellinex Digital Forge - Innovating Brands, Elevating Experiences',
  description: 'Web Design. Branding. Marketing. All in One. Intellinex is your global partner in creating powerful digital solutions that inspire and convert.',
  keywords: 'web design, branding, digital marketing, web development, Kenya, global',
  authors: [{ name: 'Intellinex Limited' }],
  creator: 'Intellinex Limited',
  publisher: 'Intellinex Limited',
  openGraph: {
    title: 'Intellinex Digital Forge - Innovating Brands, Elevating Experiences',
    description: 'Web Design. Branding. Marketing. All in One. Intellinex is your global partner in creating powerful digital solutions that inspire and convert.',
    url: 'https://intellinex.net',
    siteName: 'Intellinex Digital Forge',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Intellinex Digital Forge - Innovating Brands, Elevating Experiences',
    description: 'Web Design. Branding. Marketing. All in One. Intellinex is your global partner in creating powerful digital solutions that inspire and convert.',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <ReactQueryProvider>
          <TooltipProvider>
            {children}
            <Toaster />
            <Sonner />
          </TooltipProvider>
        </ReactQueryProvider>
      </body>
    </html>
  )
}
