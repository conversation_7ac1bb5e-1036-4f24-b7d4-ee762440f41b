(()=>{var e={};e.id=974,e.ids=[974],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1125:(e,r,t)=>{Promise.resolve().then(t.bind(t,6757)),Promise.resolve().then(t.bind(t,6892))},1611:(e,r,t)=>{"use strict";t.d(r,{$:()=>m});var s=t(687),o=t(3210),a=t(8730),l=t(9384);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=l.$;var d=t(1743);let c=((e,r)=>t=>{var s;if((null==r?void 0:r.variants)==null)return i(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:o,defaultVariants:a}=r,l=Object.keys(o).map(e=>{let r=null==t?void 0:t[e],s=null==a?void 0:a[e];if(null===r)return null;let l=n(r)||n(s);return o[e][l]}),d=t&&Object.entries(t).reduce((e,r)=>{let[t,s]=r;return void 0===s||(e[t]=s),e},{});return i(e,l,null==r||null==(s=r.compoundVariants)?void 0:s.reduce((e,r)=>{let{class:t,className:s,...o}=r;return Object.entries(o).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...a,...d}[r]):({...a,...d})[r]===t})?[...e,t,s]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)})("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),m=o.forwardRef(({className:e,variant:r,size:t,asChild:o=!1,...l},n)=>{let i=o?a.DX:"button";return(0,s.jsx)(i,{className:(0,d.cn)(c({variant:r,size:t,className:e})),ref:n,...l})});m.displayName="Button"},1743:(e,r,t)=>{"use strict";t.d(r,{cn:()=>J});var s=t(9384);let o=e=>{let r=i(e),{conflictingClassGroups:t,conflictingClassGroupModifiers:s}=e;return{getClassGroupId:e=>{let t=e.split("-");return""===t[0]&&1!==t.length&&t.shift(),a(t,r)||n(e)},getConflictingClassGroupIds:(e,r)=>{let o=t[e]||[];return r&&s[e]?[...o,...s[e]]:o}}},a=(e,r)=>{if(0===e.length)return r.classGroupId;let t=e[0],s=r.nextPart.get(t),o=s?a(e.slice(1),s):void 0;if(o)return o;if(0===r.validators.length)return;let l=e.join("-");return r.validators.find(({validator:e})=>e(l))?.classGroupId},l=/^\[(.+)\]$/,n=e=>{if(l.test(e)){let r=l.exec(e)[1],t=r?.substring(0,r.indexOf(":"));if(t)return"arbitrary.."+t}},i=e=>{let{theme:r,prefix:t}=e,s={nextPart:new Map,validators:[]};return x(Object.entries(e.classGroups),t).forEach(([e,t])=>{d(t,s,e,r)}),s},d=(e,r,t,s)=>{e.forEach(e=>{if("string"==typeof e){(""===e?r:c(r,e)).classGroupId=t;return}if("function"==typeof e)return m(e)?void d(e(s),r,t,s):void r.validators.push({validator:e,classGroupId:t});Object.entries(e).forEach(([e,o])=>{d(o,c(r,e),t,s)})})},c=(e,r)=>{let t=e;return r.split("-").forEach(e=>{t.nextPart.has(e)||t.nextPart.set(e,{nextPart:new Map,validators:[]}),t=t.nextPart.get(e)}),t},m=e=>e.isThemeGetter,x=(e,r)=>r?e.map(([e,t])=>[e,t.map(e=>"string"==typeof e?r+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,t])=>[r+e,t])):e)]):e,p=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let r=0,t=new Map,s=new Map,o=(o,a)=>{t.set(o,a),++r>e&&(r=0,s=t,t=new Map)};return{get(e){let r=t.get(e);return void 0!==r?r:void 0!==(r=s.get(e))?(o(e,r),r):void 0},set(e,r){t.has(e)?t.set(e,r):o(e,r)}}},u=e=>{let{separator:r,experimentalParseClassName:t}=e,s=1===r.length,o=r[0],a=r.length,l=e=>{let t,l=[],n=0,i=0;for(let d=0;d<e.length;d++){let c=e[d];if(0===n){if(c===o&&(s||e.slice(d,d+a)===r)){l.push(e.slice(i,d)),i=d+a;continue}if("/"===c){t=d;continue}}"["===c?n++:"]"===c&&n--}let d=0===l.length?e:e.substring(i),c=d.startsWith("!"),m=c?d.substring(1):d;return{modifiers:l,hasImportantModifier:c,baseClassName:m,maybePostfixModifierPosition:t&&t>i?t-i:void 0}};return t?e=>t({className:e,parseClassName:l}):l},h=e=>{if(e.length<=1)return e;let r=[],t=[];return e.forEach(e=>{"["===e[0]?(r.push(...t.sort(),e),t=[]):t.push(e)}),r.push(...t.sort()),r},b=e=>({cache:p(e.cacheSize),parseClassName:u(e),...o(e)}),f=/\s+/,g=(e,r)=>{let{parseClassName:t,getClassGroupId:s,getConflictingClassGroupIds:o}=r,a=[],l=e.trim().split(f),n="";for(let e=l.length-1;e>=0;e-=1){let r=l[e],{modifiers:i,hasImportantModifier:d,baseClassName:c,maybePostfixModifierPosition:m}=t(r),x=!!m,p=s(x?c.substring(0,m):c);if(!p){if(!x||!(p=s(c))){n=r+(n.length>0?" "+n:n);continue}x=!1}let u=h(i).join(":"),b=d?u+"!":u,f=b+p;if(a.includes(f))continue;a.push(f);let g=o(p,x);for(let e=0;e<g.length;++e){let r=g[e];a.push(b+r)}n=r+(n.length>0?" "+n:n)}return n};function v(){let e,r,t=0,s="";for(;t<arguments.length;)(e=arguments[t++])&&(r=y(e))&&(s&&(s+=" "),s+=r);return s}let y=e=>{let r;if("string"==typeof e)return e;let t="";for(let s=0;s<e.length;s++)e[s]&&(r=y(e[s]))&&(t&&(t+=" "),t+=r);return t},j=e=>{let r=r=>r[e]||[];return r.isThemeGetter=!0,r},w=/^\[(?:([a-z-]+):)?(.+)\]$/i,N=/^\d+\/\d+$/,k=new Set(["px","full","screen"]),C=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,z=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,A=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,P=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,E=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,I=e=>M(e)||k.has(e)||N.test(e),D=e=>F(e,"length",H),M=e=>!!e&&!Number.isNaN(Number(e)),S=e=>F(e,"number",M),W=e=>!!e&&Number.isInteger(Number(e)),$=e=>e.endsWith("%")&&M(e.slice(0,-1)),R=e=>w.test(e),B=e=>C.test(e),T=new Set(["length","size","percentage"]),O=e=>F(e,T,K),L=e=>F(e,"position",K),G=new Set(["image","url"]),_=e=>F(e,G,Y),q=e=>F(e,"",X),V=()=>!0,F=(e,r,t)=>{let s=w.exec(e);return!!s&&(s[1]?"string"==typeof r?s[1]===r:r.has(s[1]):t(s[2]))},H=e=>z.test(e)&&!A.test(e),K=()=>!1,X=e=>P.test(e),Y=e=>E.test(e);Symbol.toStringTag;let U=function(e,...r){let t,s,o,a=function(n){return s=(t=b(r.reduce((e,r)=>r(e),e()))).cache.get,o=t.cache.set,a=l,l(n)};function l(e){let r=s(e);if(r)return r;let a=g(e,t);return o(e,a),a}return function(){return a(v.apply(null,arguments))}}(()=>{let e=j("colors"),r=j("spacing"),t=j("blur"),s=j("brightness"),o=j("borderColor"),a=j("borderRadius"),l=j("borderSpacing"),n=j("borderWidth"),i=j("contrast"),d=j("grayscale"),c=j("hueRotate"),m=j("invert"),x=j("gap"),p=j("gradientColorStops"),u=j("gradientColorStopPositions"),h=j("inset"),b=j("margin"),f=j("opacity"),g=j("padding"),v=j("saturate"),y=j("scale"),w=j("sepia"),N=j("skew"),k=j("space"),C=j("translate"),z=()=>["auto","contain","none"],A=()=>["auto","hidden","clip","visible","scroll"],P=()=>["auto",R,r],E=()=>[R,r],T=()=>["",I,D],G=()=>["auto",M,R],F=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],H=()=>["solid","dashed","dotted","double","none"],K=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],X=()=>["start","end","center","between","around","evenly","stretch"],Y=()=>["","0",R],U=()=>["auto","avoid","all","avoid-page","page","left","right","column"],J=()=>[M,R];return{cacheSize:500,separator:":",theme:{colors:[V],spacing:[I,D],blur:["none","",B,R],brightness:J(),borderColor:[e],borderRadius:["none","","full",B,R],borderSpacing:E(),borderWidth:T(),contrast:J(),grayscale:Y(),hueRotate:J(),invert:Y(),gap:E(),gradientColorStops:[e],gradientColorStopPositions:[$,D],inset:P(),margin:P(),opacity:J(),padding:E(),saturate:J(),scale:J(),sepia:Y(),skew:J(),space:E(),translate:E()},classGroups:{aspect:[{aspect:["auto","square","video",R]}],container:["container"],columns:[{columns:[B]}],"break-after":[{"break-after":U()}],"break-before":[{"break-before":U()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...F(),R]}],overflow:[{overflow:A()}],"overflow-x":[{"overflow-x":A()}],"overflow-y":[{"overflow-y":A()}],overscroll:[{overscroll:z()}],"overscroll-x":[{"overscroll-x":z()}],"overscroll-y":[{"overscroll-y":z()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[h]}],"inset-x":[{"inset-x":[h]}],"inset-y":[{"inset-y":[h]}],start:[{start:[h]}],end:[{end:[h]}],top:[{top:[h]}],right:[{right:[h]}],bottom:[{bottom:[h]}],left:[{left:[h]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",W,R]}],basis:[{basis:P()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",R]}],grow:[{grow:Y()}],shrink:[{shrink:Y()}],order:[{order:["first","last","none",W,R]}],"grid-cols":[{"grid-cols":[V]}],"col-start-end":[{col:["auto",{span:["full",W,R]},R]}],"col-start":[{"col-start":G()}],"col-end":[{"col-end":G()}],"grid-rows":[{"grid-rows":[V]}],"row-start-end":[{row:["auto",{span:[W,R]},R]}],"row-start":[{"row-start":G()}],"row-end":[{"row-end":G()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",R]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",R]}],gap:[{gap:[x]}],"gap-x":[{"gap-x":[x]}],"gap-y":[{"gap-y":[x]}],"justify-content":[{justify:["normal",...X()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...X(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...X(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[g]}],px:[{px:[g]}],py:[{py:[g]}],ps:[{ps:[g]}],pe:[{pe:[g]}],pt:[{pt:[g]}],pr:[{pr:[g]}],pb:[{pb:[g]}],pl:[{pl:[g]}],m:[{m:[b]}],mx:[{mx:[b]}],my:[{my:[b]}],ms:[{ms:[b]}],me:[{me:[b]}],mt:[{mt:[b]}],mr:[{mr:[b]}],mb:[{mb:[b]}],ml:[{ml:[b]}],"space-x":[{"space-x":[k]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[k]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",R,r]}],"min-w":[{"min-w":[R,r,"min","max","fit"]}],"max-w":[{"max-w":[R,r,"none","full","min","max","fit","prose",{screen:[B]},B]}],h:[{h:[R,r,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[R,r,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[R,r,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[R,r,"auto","min","max","fit"]}],"font-size":[{text:["base",B,D]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",S]}],"font-family":[{font:[V]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",R]}],"line-clamp":[{"line-clamp":["none",M,S]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",I,R]}],"list-image":[{"list-image":["none",R]}],"list-style-type":[{list:["none","disc","decimal",R]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[f]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[f]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...H(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",I,D]}],"underline-offset":[{"underline-offset":["auto",I,R]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:E()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",R]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",R]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[f]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...F(),L]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",O]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},_]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[u]}],"gradient-via-pos":[{via:[u]}],"gradient-to-pos":[{to:[u]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[a]}],"rounded-s":[{"rounded-s":[a]}],"rounded-e":[{"rounded-e":[a]}],"rounded-t":[{"rounded-t":[a]}],"rounded-r":[{"rounded-r":[a]}],"rounded-b":[{"rounded-b":[a]}],"rounded-l":[{"rounded-l":[a]}],"rounded-ss":[{"rounded-ss":[a]}],"rounded-se":[{"rounded-se":[a]}],"rounded-ee":[{"rounded-ee":[a]}],"rounded-es":[{"rounded-es":[a]}],"rounded-tl":[{"rounded-tl":[a]}],"rounded-tr":[{"rounded-tr":[a]}],"rounded-br":[{"rounded-br":[a]}],"rounded-bl":[{"rounded-bl":[a]}],"border-w":[{border:[n]}],"border-w-x":[{"border-x":[n]}],"border-w-y":[{"border-y":[n]}],"border-w-s":[{"border-s":[n]}],"border-w-e":[{"border-e":[n]}],"border-w-t":[{"border-t":[n]}],"border-w-r":[{"border-r":[n]}],"border-w-b":[{"border-b":[n]}],"border-w-l":[{"border-l":[n]}],"border-opacity":[{"border-opacity":[f]}],"border-style":[{border:[...H(),"hidden"]}],"divide-x":[{"divide-x":[n]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[n]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[f]}],"divide-style":[{divide:H()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...H()]}],"outline-offset":[{"outline-offset":[I,R]}],"outline-w":[{outline:[I,D]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:T()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[f]}],"ring-offset-w":[{"ring-offset":[I,D]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",B,q]}],"shadow-color":[{shadow:[V]}],opacity:[{opacity:[f]}],"mix-blend":[{"mix-blend":[...K(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":K()}],filter:[{filter:["","none"]}],blur:[{blur:[t]}],brightness:[{brightness:[s]}],contrast:[{contrast:[i]}],"drop-shadow":[{"drop-shadow":["","none",B,R]}],grayscale:[{grayscale:[d]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[m]}],saturate:[{saturate:[v]}],sepia:[{sepia:[w]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[t]}],"backdrop-brightness":[{"backdrop-brightness":[s]}],"backdrop-contrast":[{"backdrop-contrast":[i]}],"backdrop-grayscale":[{"backdrop-grayscale":[d]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[m]}],"backdrop-opacity":[{"backdrop-opacity":[f]}],"backdrop-saturate":[{"backdrop-saturate":[v]}],"backdrop-sepia":[{"backdrop-sepia":[w]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[l]}],"border-spacing-x":[{"border-spacing-x":[l]}],"border-spacing-y":[{"border-spacing-y":[l]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",R]}],duration:[{duration:J()}],ease:[{ease:["linear","in","out","in-out",R]}],delay:[{delay:J()}],animate:[{animate:["none","spin","ping","pulse","bounce",R]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[y]}],"scale-x":[{"scale-x":[y]}],"scale-y":[{"scale-y":[y]}],rotate:[{rotate:[W,R]}],"translate-x":[{"translate-x":[C]}],"translate-y":[{"translate-y":[C]}],"skew-x":[{"skew-x":[N]}],"skew-y":[{"skew-y":[N]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",R]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",R]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":E()}],"scroll-mx":[{"scroll-mx":E()}],"scroll-my":[{"scroll-my":E()}],"scroll-ms":[{"scroll-ms":E()}],"scroll-me":[{"scroll-me":E()}],"scroll-mt":[{"scroll-mt":E()}],"scroll-mr":[{"scroll-mr":E()}],"scroll-mb":[{"scroll-mb":E()}],"scroll-ml":[{"scroll-ml":E()}],"scroll-p":[{"scroll-p":E()}],"scroll-px":[{"scroll-px":E()}],"scroll-py":[{"scroll-py":E()}],"scroll-ps":[{"scroll-ps":E()}],"scroll-pe":[{"scroll-pe":E()}],"scroll-pt":[{"scroll-pt":E()}],"scroll-pr":[{"scroll-pr":E()}],"scroll-pb":[{"scroll-pb":E()}],"scroll-pl":[{"scroll-pl":E()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",R]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[I,D,S]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}});function J(...e){return U((0,s.$)(e))}},2688:(e,r,t)=>{"use strict";t.d(r,{A:()=>i});var s=t(3210);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim();var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=(0,s.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:o,className:n="",children:i,iconNode:d,...c},m)=>(0,s.createElement)("svg",{ref:m,...l,width:r,height:r,stroke:e,strokeWidth:o?24*Number(t)/Number(r):t,className:a("lucide",n),...c},[...d.map(([e,r])=>(0,s.createElement)(e,r)),...Array.isArray(i)?i:[i]])),i=(e,r)=>{let t=(0,s.forwardRef)(({className:t,...l},i)=>(0,s.createElement)(n,{ref:i,iconNode:r,className:a(`lucide-${o(e)}`,t),...l}));return t.displayName=`${e}`,t}},2871:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var s=t(5239),o=t(8088),a=t(8170),l=t.n(a),n=t(893),i={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);t.d(r,i);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,8583)),"C:\\projects\\intellinex-digital-forge\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,5433)),"C:\\projects\\intellinex-digital-forge\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,2366)),"C:\\projects\\intellinex-digital-forge\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\projects\\intellinex-digital-forge\\app\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},6757:(e,r,t)=>{"use strict";t.d(r,{default:()=>g});var s=t(687),o=t(1611),a=t(3210),l=t(1743);let n=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));n.displayName="Card";let i=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",e),...r}));i.displayName="CardHeader";let d=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));d.displayName="CardTitle",a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",e),...r})).displayName="CardDescription";let c=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent",a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter";let m=a.forwardRef(({className:e,type:r,...t},o)=>(0,s.jsx)("input",{type:r,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:o,...t}));m.displayName="Input";let x=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:t,...r}));x.displayName="Textarea";var p=t(2688);let u=(0,p.A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),h=(0,p.A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]),b=(0,p.A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),f=(0,p.A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]),g=()=>(0,s.jsx)("section",{id:"contact",className:"py-20 bg-background",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"text-center mb-16 animate-fade-in-up",children:[(0,s.jsx)("h2",{className:"text-4xl lg:text-5xl font-bold text-foreground mb-6",children:"Ready to Elevate Your Brand?"}),(0,s.jsx)("p",{className:"text-xl text-muted-foreground max-w-3xl mx-auto",children:"Let's turn your ideas into smart, stunning digital solutions."})]}),(0,s.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12",children:[(0,s.jsxs)(n,{className:"p-8 animate-scale-in",children:[(0,s.jsxs)(i,{className:"px-0 pt-0",children:[(0,s.jsx)(d,{className:"text-2xl font-bold text-foreground",children:"Start Your Project"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Tell us about your project or idea — we're ready to bring it to life."})]}),(0,s.jsx)(c,{className:"px-0 pb-0",children:(0,s.jsxs)("form",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-foreground mb-2",children:"First Name"}),(0,s.jsx)(m,{placeholder:"John",className:"border-border focus:border-primary"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-foreground mb-2",children:"Last Name"}),(0,s.jsx)(m,{placeholder:"Doe",className:"border-border focus:border-primary"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-foreground mb-2",children:"Email Address"}),(0,s.jsx)(m,{type:"email",placeholder:"<EMAIL>",className:"border-border focus:border-primary"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-foreground mb-2",children:"Company (Optional)"}),(0,s.jsx)(m,{placeholder:"Your Company",className:"border-border focus:border-primary"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-foreground mb-2",children:"Project Type"}),(0,s.jsxs)("select",{className:"w-full px-3 py-2 border border-border rounded-md focus:border-primary focus:ring-1 focus:ring-primary bg-background text-foreground",children:[(0,s.jsx)("option",{children:"Select a service"}),(0,s.jsx)("option",{children:"Web Design & Development"}),(0,s.jsx)("option",{children:"Branding & Design"}),(0,s.jsx)("option",{children:"Digital Marketing"}),(0,s.jsx)("option",{children:"Full Package"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-foreground mb-2",children:"Project Details"}),(0,s.jsx)(x,{placeholder:"Tell us about your project, goals, and requirements...",rows:4,className:"border-border focus:border-primary resize-none"})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,s.jsx)(o.$,{type:"submit",className:"flex-1 brand-gradient text-primary-foreground hover:opacity-90 transition-opacity",children:"Start Your Project"}),(0,s.jsx)(o.$,{variant:"outline",className:"flex-1 border-primary text-primary hover:bg-primary hover:text-primary-foreground",children:"Book a Free Call"})]})]})})]}),(0,s.jsxs)("div",{className:"space-y-8 animate-fade-in-up",children:[(0,s.jsx)(n,{className:"p-6 hover:shadow-lg transition-shadow",children:(0,s.jsx)(c,{className:"p-0",children:(0,s.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center flex-shrink-0",children:(0,s.jsx)(u,{className:"w-6 h-6 text-primary"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-foreground mb-1",children:"Email Us"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-2",children:"Ready to discuss your project?"}),(0,s.jsx)("a",{href:"mailto:<EMAIL>",className:"text-primary hover:underline font-medium",children:"<EMAIL>"})]})]})})}),(0,s.jsx)(n,{className:"p-6 hover:shadow-lg transition-shadow",children:(0,s.jsx)(c,{className:"p-0",children:(0,s.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center flex-shrink-0",children:(0,s.jsx)(h,{className:"w-6 h-6 text-primary"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-foreground mb-1",children:"Location"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-2",children:"Based in Kenya, serving globally"}),(0,s.jsx)("p",{className:"text-primary font-medium",children:"Nairobi, Kenya"})]})]})})}),(0,s.jsx)(n,{className:"p-6 hover:shadow-lg transition-shadow",children:(0,s.jsx)(c,{className:"p-0",children:(0,s.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center flex-shrink-0",children:(0,s.jsx)(b,{className:"w-6 h-6 text-primary"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-foreground mb-1",children:"Business Hours"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-2",children:"We're here when you need us"}),(0,s.jsx)("p",{className:"text-primary font-medium",children:"Monday – Friday | 9am – 6pm EAT"})]})]})})}),(0,s.jsxs)("div",{className:"bg-primary/5 rounded-2xl p-6",children:[(0,s.jsx)("h4",{className:"font-bold text-foreground mb-4",children:"Quick Actions"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)(o.$,{variant:"outline",className:"w-full justify-start border-primary/20 hover:border-primary",children:[(0,s.jsx)(u,{className:"w-4 h-4 mr-2"}),"Download Our Portfolio PDF"]}),(0,s.jsxs)(o.$,{variant:"outline",className:"w-full justify-start border-primary/20 hover:border-primary",children:[(0,s.jsx)(f,{className:"w-4 h-4 mr-2"}),"Schedule a Discovery Call"]})]})]}),(0,s.jsx)(n,{className:"p-6 bg-primary text-primary-foreground",children:(0,s.jsxs)(c,{className:"p-0",children:[(0,s.jsx)("h4",{className:"font-bold mb-2",children:"Join Our Creative Tech Circle"}),(0,s.jsx)("p",{className:"text-primary-foreground/80 mb-4 text-sm",children:"Get insights, tips & case studies delivered to your inbox."}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(m,{placeholder:"Enter your email",className:"bg-white/10 border-white/20 text-primary-foreground placeholder:text-primary-foreground/60"}),(0,s.jsx)(o.$,{variant:"secondary",className:"bg-white text-primary hover:bg-white/90",children:"Subscribe"})]})]})})]})]})]})})},6892:(e,r,t)=>{"use strict";t.d(r,{default:()=>d});var s=t(687),o=t(3210),a=t(1611),l=t(2688);let n=(0,l.A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),i=(0,l.A)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]),d=()=>{let[e,r]=(0,o.useState)(!1),t=()=>r(!e);return(0,s.jsxs)("nav",{className:"fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-md border-b border-border/50",children:[(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("h1",{className:"text-2xl font-bold text-gradient",children:"INTELLINEX"})}),(0,s.jsx)("div",{className:"hidden md:block",children:(0,s.jsxs)("div",{className:"ml-10 flex items-baseline space-x-8",children:[(0,s.jsx)("a",{href:"#home",className:"text-foreground hover:text-primary transition-colors font-medium",onClick:e=>{e.preventDefault(),document.getElementById("home")?.scrollIntoView({behavior:"smooth"})},children:"Home"}),(0,s.jsx)("a",{href:"#services",className:"text-foreground hover:text-primary transition-colors font-medium",onClick:e=>{e.preventDefault(),document.getElementById("services")?.scrollIntoView({behavior:"smooth"})},children:"Services"}),(0,s.jsx)("a",{href:"#about",className:"text-foreground hover:text-primary transition-colors font-medium",onClick:e=>{e.preventDefault(),document.getElementById("about")?.scrollIntoView({behavior:"smooth"})},children:"About"}),(0,s.jsx)("a",{href:"#contact",className:"text-foreground hover:text-primary transition-colors font-medium",onClick:e=>{e.preventDefault(),document.getElementById("contact")?.scrollIntoView({behavior:"smooth"})},children:"Contact"})]})}),(0,s.jsx)("div",{className:"hidden md:block",children:(0,s.jsx)(a.$,{className:"brand-gradient text-primary-foreground hover:opacity-90 transition-opacity",children:"Let's Build Together"})}),(0,s.jsx)("div",{className:"md:hidden",children:(0,s.jsx)(a.$,{variant:"ghost",size:"icon",onClick:t,className:"text-foreground hover:text-primary",children:e?(0,s.jsx)(n,{className:"h-6 w-6"}):(0,s.jsx)(i,{className:"h-6 w-6"})})})]})}),e&&(0,s.jsx)("div",{className:"md:hidden",children:(0,s.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-background border-b border-border",children:[(0,s.jsx)("a",{href:"#home",className:"block px-3 py-2 text-foreground hover:text-primary transition-colors font-medium",onClick:e=>{e.preventDefault(),document.getElementById("home")?.scrollIntoView({behavior:"smooth"}),t()},children:"Home"}),(0,s.jsx)("a",{href:"#services",className:"block px-3 py-2 text-foreground hover:text-primary transition-colors font-medium",onClick:e=>{e.preventDefault(),document.getElementById("services")?.scrollIntoView({behavior:"smooth"}),t()},children:"Services"}),(0,s.jsx)("a",{href:"#about",className:"block px-3 py-2 text-foreground hover:text-primary transition-colors font-medium",onClick:e=>{e.preventDefault(),document.getElementById("about")?.scrollIntoView({behavior:"smooth"}),t()},children:"About"}),(0,s.jsx)("a",{href:"#contact",className:"block px-3 py-2 text-foreground hover:text-primary transition-colors font-medium",onClick:e=>{e.preventDefault(),document.getElementById("contact")?.scrollIntoView({behavior:"smooth"}),t()},children:"Contact"}),(0,s.jsx)("div",{className:"px-3 py-2",children:(0,s.jsx)(a.$,{className:"w-full brand-gradient text-primary-foreground hover:opacity-90 transition-opacity",children:"Let's Build Together"})})]})})]})}},7973:(e,r,t)=>{Promise.resolve().then(t.bind(t,8840)),Promise.resolve().then(t.bind(t,8928))},8583:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>A});var s=t(7413),o=t(8928),a=t(3469),l=t(6373);let n=(0,l.A)("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]),i=()=>(0,s.jsxs)("section",{id:"home",className:"relative min-h-screen flex items-center justify-center overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 hero-gradient"}),(0,s.jsx)("div",{className:"absolute inset-0 dotted-world-map"}),(0,s.jsxs)("div",{className:"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,s.jsxs)("div",{className:"animate-fade-in-up",children:[(0,s.jsxs)("h1",{className:"text-5xl sm:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight",children:["Innovating Brands,",(0,s.jsx)("br",{}),(0,s.jsx)("span",{className:"text-accent",children:"Elevating Experiences."})]}),(0,s.jsxs)("p",{className:"text-xl sm:text-2xl text-white/90 mb-8 max-w-3xl mx-auto leading-relaxed",children:["Web Design. Branding. Marketing. All in One.",(0,s.jsx)("br",{}),"Intellinex is your global partner in creating powerful digital solutions that inspire and convert."]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[(0,s.jsx)(a.$,{size:"lg",className:"bg-white text-primary hover:bg-white/90 transition-all transform hover:scale-105 px-8 py-4 text-lg font-semibold",children:"Let's Build Together"}),(0,s.jsx)(a.$,{variant:"outline",size:"lg",className:"border-white text-white hover:bg-white hover:text-primary transition-all transform hover:scale-105 px-8 py-4 text-lg font-semibold",children:"View Our Work"})]})]}),(0,s.jsx)("div",{className:"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce",children:(0,s.jsx)(n,{className:"text-white/60 w-6 h-6"})})]}),(0,s.jsx)("div",{className:"absolute bottom-0 left-0 right-0 bg-white/10 backdrop-blur-sm border-t border-white/20",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,s.jsx)("p",{className:"text-center text-white/80 text-sm font-medium",children:"Trusted by Bold Brands Worldwide"})})})]});var d=t(1120),c=t(974);let m=d.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,c.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));m.displayName="Card";let x=d.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,c.cn)("flex flex-col space-y-1.5 p-6",e),...r}));x.displayName="CardHeader";let p=d.forwardRef(({className:e,...r},t)=>(0,s.jsx)("h3",{ref:t,className:(0,c.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));p.displayName="CardTitle";let u=d.forwardRef(({className:e,...r},t)=>(0,s.jsx)("p",{ref:t,className:(0,c.cn)("text-sm text-muted-foreground",e),...r}));u.displayName="CardDescription";let h=d.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,c.cn)("p-6 pt-0",e),...r}));h.displayName="CardContent",d.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,c.cn)("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter";let b=(0,l.A)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]),f=(0,l.A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]),g=(0,l.A)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]]),v=()=>{let e=[{icon:(0,s.jsx)(b,{className:"w-12 h-12 text-primary"}),title:"Web Design & Development",description:"We craft responsive, lightning-fast websites that don't just look amazing — they drive results. From custom web apps to high-converting landing pages, your online presence starts here.",features:["WordPress","Next.js","E-commerce","Web Portals"],gradient:"from-blue-500 to-purple-600"},{icon:(0,s.jsx)(f,{className:"w-12 h-12 text-primary"}),title:"Branding & Design",description:"We build brands from scratch and refine existing ones — logos, visual identity, print & digital assets, all with a cohesive story.",features:["Logo Design","Brand Guidelines","Posters & Banners","Print Media","T-Shirt Printing"],gradient:"from-purple-500 to-pink-600"},{icon:(0,s.jsx)(g,{className:"w-12 h-12 text-primary"}),title:"Digital Marketing",description:"Our campaigns are smart, data-driven, and designed to scale your impact across the web.",features:["Social Media Strategy","Paid Ads","Email Marketing","SEO","Analytics"],gradient:"from-pink-500 to-red-600"}];return(0,s.jsx)("section",{id:"services",className:"py-20 bg-background",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"text-center mb-16 animate-fade-in-up",children:[(0,s.jsx)("h2",{className:"text-4xl lg:text-5xl font-bold text-foreground mb-6",children:"What We Do"}),(0,s.jsx)("p",{className:"text-xl text-muted-foreground max-w-3xl mx-auto",children:"Full-spectrum digital solutions designed to transform your business and captivate your audience."})]}),(0,s.jsx)("div",{className:"grid md:grid-cols-3 gap-8",children:e.map((e,r)=>(0,s.jsxs)(m,{className:"group hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border-border/50 hover:border-primary/20 animate-scale-in",style:{animationDelay:`${.2*r}s`},children:[(0,s.jsxs)(x,{className:"text-center pb-4",children:[(0,s.jsx)("div",{className:"mx-auto mb-4 p-3 rounded-xl bg-primary/10 w-fit group-hover:bg-primary/20 transition-colors",children:e.icon}),(0,s.jsx)(p,{className:"text-2xl font-bold text-foreground mb-2",children:e.title})]}),(0,s.jsxs)(h,{className:"text-center",children:[(0,s.jsx)(u,{className:"text-muted-foreground mb-6 leading-relaxed",children:e.description}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2 justify-center mb-6",children:e.features.map((e,r)=>(0,s.jsx)("span",{className:"px-3 py-1 bg-primary/10 text-primary rounded-full text-sm font-medium",children:e},r))}),(0,s.jsx)(a.$,{variant:"outline",className:"w-full hover:bg-primary hover:text-primary-foreground transition-all",children:"Learn More"})]})]},r))}),(0,s.jsxs)("div",{className:"mt-20 text-center",children:[(0,s.jsx)("h3",{className:"text-3xl lg:text-4xl font-bold text-foreground mb-12",children:"Why Intellinex?"}),(0,s.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:[{title:"Full-Spectrum Expertise",description:"One team, all your digital needs."},{title:"International Reach",description:"We work across time zones, borders, and expectations."},{title:"Creative + Tech",description:"Code meets design. Strategy meets execution."},{title:"Client-Centered",description:"We listen. We collaborate. We deliver."}].map((e,r)=>(0,s.jsxs)("div",{className:"text-center group",children:[(0,s.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 bg-primary/10 rounded-xl flex items-center justify-center group-hover:bg-primary/20 transition-colors",children:(0,s.jsx)("span",{className:"text-2xl font-bold text-primary",children:"✓"})}),(0,s.jsx)("h4",{className:"text-lg font-semibold text-foreground mb-2",children:e.title}),(0,s.jsx)("p",{className:"text-muted-foreground",children:e.description})]},r))})]})]})})},y=()=>(0,s.jsx)("section",{id:"about",className:"py-20 bg-muted/30",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"text-center mb-16 animate-fade-in-up",children:[(0,s.jsx)("h2",{className:"text-4xl lg:text-5xl font-bold text-foreground mb-6",children:"About Intellinex"}),(0,s.jsx)("p",{className:"text-xl text-muted-foreground max-w-3xl mx-auto",children:"Transforming ideas into powerful digital experiences that drive growth and innovation."})]}),(0,s.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 items-center mb-16",children:[(0,s.jsxs)("div",{className:"animate-fade-in-up",children:[(0,s.jsx)("h3",{className:"text-3xl font-bold text-foreground mb-6",children:"Who We Are"}),(0,s.jsx)("p",{className:"text-lg text-muted-foreground mb-6 leading-relaxed",children:"Intellinex was born out of one opportunity — and a bigger vision. We started with one international client. Today, we're building digital experiences for brands around the globe."}),(0,s.jsx)("p",{className:"text-lg text-muted-foreground leading-relaxed",children:"Our team combines technical expertise with creative vision to deliver solutions that don't just meet expectations — they exceed them."})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4 animate-scale-in",children:[(0,s.jsx)(m,{className:"text-center p-6 hover:shadow-lg transition-shadow",children:(0,s.jsxs)(h,{className:"p-0",children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-primary mb-2",children:"50+"}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:"Projects Delivered"})]})}),(0,s.jsx)(m,{className:"text-center p-6 hover:shadow-lg transition-shadow",children:(0,s.jsxs)(h,{className:"p-0",children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-primary mb-2",children:"15+"}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:"Countries Served"})]})}),(0,s.jsx)(m,{className:"text-center p-6 hover:shadow-lg transition-shadow",children:(0,s.jsxs)(h,{className:"p-0",children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-primary mb-2",children:"100%"}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:"Client Satisfaction"})]})}),(0,s.jsx)(m,{className:"text-center p-6 hover:shadow-lg transition-shadow",children:(0,s.jsxs)(h,{className:"p-0",children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-primary mb-2",children:"24/7"}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:"Support Available"})]})})]})]}),(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-8 mb-16",children:[(0,s.jsx)(m,{className:"p-8 hover:shadow-lg transition-shadow",children:(0,s.jsxs)(h,{className:"p-0",children:[(0,s.jsx)("h4",{className:"text-2xl font-bold text-foreground mb-4",children:"Our Mission"}),(0,s.jsx)("p",{className:"text-muted-foreground leading-relaxed",children:"To help businesses of all sizes unlock growth and deliver better experiences through modern technology and design."})]})}),(0,s.jsx)(m,{className:"p-8 hover:shadow-lg transition-shadow",children:(0,s.jsxs)(h,{className:"p-0",children:[(0,s.jsx)("h4",{className:"text-2xl font-bold text-foreground mb-4",children:"Our Vision"}),(0,s.jsx)("p",{className:"text-muted-foreground leading-relaxed",children:"To be the most trusted tech & creative partner for international businesses seeking powerful, human-centered digital solutions."})]})})]}),(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsx)("h3",{className:"text-3xl lg:text-4xl font-bold text-foreground mb-12",children:"Our Values"}),(0,s.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:[{title:"Integrity & Transparency",description:"We believe in honest communication and delivering exactly what we promise."},{title:"Creativity With Purpose",description:"Every design decision serves a strategic goal and enhances user experience."},{title:"Detail-Driven Execution",description:"From pixel-perfect designs to flawless code, we sweat the small stuff."},{title:"Global Perspective, Local Insight",description:"We understand diverse markets while maintaining cultural sensitivity."}].map((e,r)=>(0,s.jsx)(m,{className:"p-6 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1",children:(0,s.jsxs)(h,{className:"p-0 text-center",children:[(0,s.jsx)("div",{className:"w-12 h-12 mx-auto mb-4 bg-primary/10 rounded-xl flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-xl font-bold text-primary",children:"✦"})}),(0,s.jsx)("h4",{className:"text-lg font-semibold text-foreground mb-3",children:e.title}),(0,s.jsx)("p",{className:"text-muted-foreground text-sm leading-relaxed",children:e.description})]})},r))})]}),(0,s.jsxs)("div",{className:"bg-primary/5 rounded-2xl p-8 lg:p-12",children:[(0,s.jsx)("h3",{className:"text-3xl font-bold text-foreground text-center mb-12",children:"What Our Clients Say"}),(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-8",children:[(0,s.jsx)(m,{className:"p-6",children:(0,s.jsx)(h,{className:"p-0",children:(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("div",{className:"flex text-primary mb-2",children:"★".repeat(5)}),(0,s.jsx)("p",{className:"text-muted-foreground italic mb-4",children:'"We were looking for a freelance developer. What we found in Intellinex was a whole ecosystem of ideas, design, and strategy."'}),(0,s.jsxs)("div",{className:"text-sm",children:[(0,s.jsx)("div",{className:"font-semibold text-foreground",children:"Alex M."}),(0,s.jsx)("div",{className:"text-muted-foreground",children:"UK Client"})]})]})})}),(0,s.jsx)(m,{className:"p-6",children:(0,s.jsx)(h,{className:"p-0",children:(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("div",{className:"flex text-primary mb-2",children:"★".repeat(5)}),(0,s.jsx)("p",{className:"text-muted-foreground italic mb-4",children:'"They didn\'t just build us a website — they built us a brand."'}),(0,s.jsxs)("div",{className:"text-sm",children:[(0,s.jsx)("div",{className:"font-semibold text-foreground",children:"Doreen M."}),(0,s.jsx)("div",{className:"text-muted-foreground",children:"Kenya"})]})]})})})]})]})]})});var j=t(8840);let w=d.forwardRef(({className:e,type:r,...t},o)=>(0,s.jsx)("input",{type:r,className:(0,c.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:o,...t}));w.displayName="Input";let N=(0,l.A)("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]]),k=(0,l.A)("Instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]),C=(0,l.A)("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]),z=()=>(0,s.jsx)("footer",{className:"bg-foreground text-background",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"py-12 grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,s.jsxs)("div",{className:"lg:col-span-2",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-accent mb-4",children:"INTELLINEX"}),(0,s.jsx)("p",{className:"text-background/80 mb-6 max-w-md leading-relaxed",children:"Your global partner in creating powerful digital solutions that inspire and convert. From web design to branding and marketing — we've got you covered."}),(0,s.jsxs)("div",{className:"max-w-md",children:[(0,s.jsx)("h4",{className:"font-semibold mb-3",children:"Stay Updated"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(w,{placeholder:"Enter your email",className:"bg-white/10 border-white/20 text-background placeholder:text-background/60"}),(0,s.jsx)(a.$,{variant:"secondary",className:"bg-accent text-accent-foreground hover:bg-accent/90",children:"Subscribe"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold mb-4",children:"Quick Links"}),(0,s.jsx)("ul",{className:"space-y-3",children:[{name:"Home",href:"#home"},{name:"Services",href:"#services"},{name:"About",href:"#about"},{name:"Contact",href:"#contact"}].map(e=>(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:e.href,className:"text-background/80 hover:text-accent transition-colors",children:e.name})},e.name))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold mb-4",children:"Services"}),(0,s.jsx)("ul",{className:"space-y-3",children:["Web Development","Brand Design","Digital Marketing","Print Media","E-commerce Solutions"].map(e=>(0,s.jsx)("li",{children:(0,s.jsx)("span",{className:"text-background/80",children:e})},e))})]})]}),(0,s.jsx)("div",{className:"border-t border-background/20 py-6",children:(0,s.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,s.jsx)("div",{className:"text-background/60 text-sm",children:"\xa9 2024 Intellinex Limited. All rights reserved."}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("span",{className:"text-sm text-background/60",children:"Follow us:"}),(0,s.jsxs)("div",{className:"flex space-x-3",children:[(0,s.jsx)(a.$,{variant:"ghost",size:"icon",className:"text-background/60 hover:text-accent hover:bg-white/10",children:(0,s.jsx)(N,{className:"w-4 h-4"})}),(0,s.jsx)(a.$,{variant:"ghost",size:"icon",className:"text-background/60 hover:text-accent hover:bg-white/10",children:(0,s.jsx)(k,{className:"w-4 h-4"})}),(0,s.jsx)(a.$,{variant:"ghost",size:"icon",className:"text-background/60 hover:text-accent hover:bg-white/10",children:(0,s.jsx)(C,{className:"w-4 h-4"})})]})]}),(0,s.jsx)("div",{className:"text-background/60 text-sm",children:(0,s.jsx)("a",{href:"mailto:<EMAIL>",className:"hover:text-accent transition-colors",children:"<EMAIL>"})})]})})]})});function A(){return(0,s.jsxs)("div",{className:"min-h-screen",children:[(0,s.jsx)(o.default,{}),(0,s.jsx)(i,{}),(0,s.jsx)(v,{}),(0,s.jsx)(y,{}),(0,s.jsx)(j.default,{}),(0,s.jsx)(z,{})]})}},8840:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\intellinex-digital-forge\\\\src\\\\components\\\\Contact.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\intellinex-digital-forge\\src\\components\\Contact.tsx","default")},8928:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\intellinex-digital-forge\\\\src\\\\components\\\\Navigation.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\intellinex-digital-forge\\src\\components\\Navigation.tsx","default")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9384:(e,r,t)=>{"use strict";function s(){for(var e,r,t=0,s="",o=arguments.length;t<o;t++)(e=arguments[t])&&(r=function e(r){var t,s,o="";if("string"==typeof r||"number"==typeof r)o+=r;else if("object"==typeof r)if(Array.isArray(r)){var a=r.length;for(t=0;t<a;t++)r[t]&&(s=e(r[t]))&&(o&&(o+=" "),o+=s)}else for(s in r)r[s]&&(o&&(o+=" "),o+=s);return o}(e))&&(s&&(s+=" "),s+=r);return s}t.d(r,{$:()=>s})}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[92,980],()=>t(2871));module.exports=s})();