import { Card, CardContent } from "@/components/ui/card";

const About = () => {
  const values = [
    {
      title: "Integrity & Transparency",
      description: "We believe in honest communication and delivering exactly what we promise."
    },
    {
      title: "Creativity With Purpose",
      description: "Every design decision serves a strategic goal and enhances user experience."
    },
    {
      title: "Detail-Driven Execution",
      description: "From pixel-perfect designs to flawless code, we sweat the small stuff."
    },
    {
      title: "Global Perspective, Local Insight",
      description: "We understand diverse markets while maintaining cultural sensitivity."
    }
  ];

  return (
    <section id="about" className="py-20 bg-muted/30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16 animate-fade-in-up">
          <h2 className="text-4xl lg:text-5xl font-bold text-foreground mb-6">
            About Intellinex
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Transforming ideas into powerful digital experiences that drive growth and innovation.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
          <div className="animate-fade-in-up">
            <h3 className="text-3xl font-bold text-foreground mb-6">
              Who We Are
            </h3>
            <p className="text-lg text-muted-foreground mb-6 leading-relaxed">
              Intellinex was born out of one opportunity — and a bigger vision. 
              We started with one international client. Today, we're building digital experiences for brands around the globe.
            </p>
            <p className="text-lg text-muted-foreground leading-relaxed">
              Our team combines technical expertise with creative vision to deliver solutions that don't just meet expectations — they exceed them.
            </p>
          </div>
          
          <div className="grid grid-cols-2 gap-4 animate-scale-in">
            <Card className="text-center p-6 hover:shadow-lg transition-shadow">
              <CardContent className="p-0">
                <div className="text-3xl font-bold text-primary mb-2">50+</div>
                <div className="text-sm text-muted-foreground">Projects Delivered</div>
              </CardContent>
            </Card>
            
            <Card className="text-center p-6 hover:shadow-lg transition-shadow">
              <CardContent className="p-0">
                <div className="text-3xl font-bold text-primary mb-2">15+</div>
                <div className="text-sm text-muted-foreground">Countries Served</div>
              </CardContent>
            </Card>
            
            <Card className="text-center p-6 hover:shadow-lg transition-shadow">
              <CardContent className="p-0">
                <div className="text-3xl font-bold text-primary mb-2">100%</div>
                <div className="text-sm text-muted-foreground">Client Satisfaction</div>
              </CardContent>
            </Card>
            
            <Card className="text-center p-6 hover:shadow-lg transition-shadow">
              <CardContent className="p-0">
                <div className="text-3xl font-bold text-primary mb-2">24/7</div>
                <div className="text-sm text-muted-foreground">Support Available</div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Mission & Vision */}
        <div className="grid md:grid-cols-2 gap-8 mb-16">
          <Card className="p-8 hover:shadow-lg transition-shadow">
            <CardContent className="p-0">
              <h4 className="text-2xl font-bold text-foreground mb-4">Our Mission</h4>
              <p className="text-muted-foreground leading-relaxed">
                To help businesses of all sizes unlock growth and deliver better experiences 
                through modern technology and design.
              </p>
            </CardContent>
          </Card>
          
          <Card className="p-8 hover:shadow-lg transition-shadow">
            <CardContent className="p-0">
              <h4 className="text-2xl font-bold text-foreground mb-4">Our Vision</h4>
              <p className="text-muted-foreground leading-relaxed">
                To be the most trusted tech & creative partner for international businesses 
                seeking powerful, human-centered digital solutions.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Values */}
        <div className="text-center mb-12">
          <h3 className="text-3xl lg:text-4xl font-bold text-foreground mb-12">
            Our Values
          </h3>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <Card 
                key={index} 
                className="p-6 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1"
              >
                <CardContent className="p-0 text-center">
                  <div className="w-12 h-12 mx-auto mb-4 bg-primary/10 rounded-xl flex items-center justify-center">
                    <span className="text-xl font-bold text-primary">✦</span>
                  </div>
                  <h4 className="text-lg font-semibold text-foreground mb-3">
                    {value.title}
                  </h4>
                  <p className="text-muted-foreground text-sm leading-relaxed">
                    {value.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Testimonials */}
        <div className="bg-primary/5 rounded-2xl p-8 lg:p-12">
          <h3 className="text-3xl font-bold text-foreground text-center mb-12">
            What Our Clients Say
          </h3>
          
          <div className="grid md:grid-cols-2 gap-8">
            <Card className="p-6">
              <CardContent className="p-0">
                <div className="mb-4">
                  <div className="flex text-primary mb-2">
                    {"★".repeat(5)}
                  </div>
                  <p className="text-muted-foreground italic mb-4">
                    "We were looking for a freelance developer. What we found in Intellinex was 
                    a whole ecosystem of ideas, design, and strategy."
                  </p>
                  <div className="text-sm">
                    <div className="font-semibold text-foreground">Alex M.</div>
                    <div className="text-muted-foreground">UK Client</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="p-6">
              <CardContent className="p-0">
                <div className="mb-4">
                  <div className="flex text-primary mb-2">
                    {"★".repeat(5)}
                  </div>
                  <p className="text-muted-foreground italic mb-4">
                    "They didn't just build us a website — they built us a brand."
                  </p>
                  <div className="text-sm">
                    <div className="font-semibold text-foreground">Doreen M.</div>
                    <div className="text-muted-foreground">Kenya</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;