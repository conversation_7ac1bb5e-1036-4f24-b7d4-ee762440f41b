"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Mail, Phone, Clock, Globe } from "lucide-react";

const Contact = () => {
  return (
    <section id="contact" className="py-20 bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16 animate-fade-in-up">
          <h2 className="text-4xl lg:text-5xl font-bold text-foreground mb-6">
            Ready to Elevate Your Brand?
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Let's turn your ideas into smart, stunning digital solutions.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <Card className="p-8 animate-scale-in">
            <CardHeader className="px-0 pt-0">
              <CardTitle className="text-2xl font-bold text-foreground">
                Start Your Project
              </CardTitle>
              <p className="text-muted-foreground">
                Tell us about your project or idea — we're ready to bring it to life.
              </p>
            </CardHeader>
            
            <CardContent className="px-0 pb-0">
              <form className="space-y-6">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      First Name
                    </label>
                    <Input 
                      placeholder="John" 
                      className="border-border focus:border-primary"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Last Name
                    </label>
                    <Input 
                      placeholder="Doe" 
                      className="border-border focus:border-primary"
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Email Address
                  </label>
                  <Input 
                    type="email" 
                    placeholder="<EMAIL>" 
                    className="border-border focus:border-primary"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Company (Optional)
                  </label>
                  <Input 
                    placeholder="Your Company" 
                    className="border-border focus:border-primary"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Project Type
                  </label>
                  <select className="w-full px-3 py-2 border border-border rounded-md focus:border-primary focus:ring-1 focus:ring-primary bg-background text-foreground">
                    <option>Select a service</option>
                    <option>Web Design & Development</option>
                    <option>Branding & Design</option>
                    <option>Digital Marketing</option>
                    <option>Full Package</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Project Details
                  </label>
                  <Textarea 
                    placeholder="Tell us about your project, goals, and requirements..."
                    rows={4}
                    className="border-border focus:border-primary resize-none"
                  />
                </div>
                
                <div className="flex flex-col sm:flex-row gap-4">
                  <Button 
                    type="submit" 
                    className="flex-1 brand-gradient text-primary-foreground hover:opacity-90 transition-opacity"
                  >
                    Start Your Project
                  </Button>
                  <Button 
                    variant="outline" 
                    className="flex-1 border-primary text-primary hover:bg-primary hover:text-primary-foreground"
                  >
                    Book a Free Call
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <div className="space-y-8 animate-fade-in-up">
            <Card className="p-6 hover:shadow-lg transition-shadow">
              <CardContent className="p-0">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center flex-shrink-0">
                    <Mail className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-foreground mb-1">Email Us</h4>
                    <p className="text-muted-foreground mb-2">
                      Ready to discuss your project?
                    </p>
                    <a 
                      href="mailto:<EMAIL>" 
                      className="text-primary hover:underline font-medium"
                    >
                      <EMAIL>
                    </a>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="p-6 hover:shadow-lg transition-shadow">
              <CardContent className="p-0">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center flex-shrink-0">
                    <Globe className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-foreground mb-1">Location</h4>
                    <p className="text-muted-foreground mb-2">
                      Based in Kenya, serving globally
                    </p>
                    <p className="text-primary font-medium">
                      Nairobi, Kenya
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="p-6 hover:shadow-lg transition-shadow">
              <CardContent className="p-0">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center flex-shrink-0">
                    <Clock className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-foreground mb-1">Business Hours</h4>
                    <p className="text-muted-foreground mb-2">
                      We're here when you need us
                    </p>
                    <p className="text-primary font-medium">
                      Monday – Friday | 9am – 6pm EAT
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <div className="bg-primary/5 rounded-2xl p-6">
              <h4 className="font-bold text-foreground mb-4">Quick Actions</h4>
              <div className="space-y-3">
                <Button 
                  variant="outline" 
                  className="w-full justify-start border-primary/20 hover:border-primary"
                >
                  <Mail className="w-4 h-4 mr-2" />
                  Download Our Portfolio PDF
                </Button>
                <Button 
                  variant="outline" 
                  className="w-full justify-start border-primary/20 hover:border-primary"
                >
                  <Phone className="w-4 h-4 mr-2" />
                  Schedule a Discovery Call
                </Button>
              </div>
            </div>

            {/* Newsletter Signup */}
            <Card className="p-6 bg-primary text-primary-foreground">
              <CardContent className="p-0">
                <h4 className="font-bold mb-2">Join Our Creative Tech Circle</h4>
                <p className="text-primary-foreground/80 mb-4 text-sm">
                  Get insights, tips & case studies delivered to your inbox.
                </p>
                <div className="flex gap-2">
                  <Input 
                    placeholder="Enter your email" 
                    className="bg-white/10 border-white/20 text-primary-foreground placeholder:text-primary-foreground/60"
                  />
                  <Button 
                    variant="secondary" 
                    className="bg-white text-primary hover:bg-white/90"
                  >
                    Subscribe
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;