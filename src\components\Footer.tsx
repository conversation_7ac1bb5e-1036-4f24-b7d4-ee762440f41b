import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Linkedin, Instagram, Twitter } from "lucide-react";

const Footer = () => {
  return (
    <footer className="bg-foreground text-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="py-12 grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-2">
            <h3 className="text-2xl font-bold text-accent mb-4">INTELLINEX</h3>
            <p className="text-background/80 mb-6 max-w-md leading-relaxed">
              Your global partner in creating powerful digital solutions that inspire and convert. 
              From web design to branding and marketing — we've got you covered.
            </p>
            
            {/* Newsletter Signup */}
            <div className="max-w-md">
              <h4 className="font-semibold mb-3">Stay Updated</h4>
              <div className="flex gap-2">
                <Input 
                  placeholder="Enter your email" 
                  className="bg-white/10 border-white/20 text-background placeholder:text-background/60"
                />
                <Button 
                  variant="secondary" 
                  className="bg-accent text-accent-foreground hover:bg-accent/90"
                >
                  Subscribe
                </Button>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-3">
              {[
                { name: "Home", href: "#home" },
                { name: "Services", href: "#services" },
                { name: "About", href: "#about" },
                { name: "Contact", href: "#contact" }
              ].map((link) => (
                <li key={link.name}>
                  <a 
                    href={link.href} 
                    className="text-background/80 hover:text-accent transition-colors"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Services */}
          <div>
            <h4 className="font-semibold mb-4">Services</h4>
            <ul className="space-y-3">
              {[
                "Web Development",
                "Brand Design",
                "Digital Marketing",
                "Print Media",
                "E-commerce Solutions"
              ].map((service) => (
                <li key={service}>
                  <span className="text-background/80">{service}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Bottom Footer */}
        <div className="border-t border-background/20 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            {/* Copyright */}
            <div className="text-background/60 text-sm">
              © 2024 Intellinex Limited. All rights reserved.
            </div>

            {/* Social Links */}
            <div className="flex items-center space-x-4">
              <span className="text-sm text-background/60">Follow us:</span>
              
              <div className="flex space-x-3">
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-background/60 hover:text-accent hover:bg-white/10"
                >
                  <Linkedin className="w-4 h-4" />
                </Button>
                
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-background/60 hover:text-accent hover:bg-white/10"
                >
                  <Instagram className="w-4 h-4" />
                </Button>
                
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-background/60 hover:text-accent hover:bg-white/10"
                >
                  <Twitter className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Contact Info */}
            <div className="text-background/60 text-sm">
              <a 
                href="mailto:<EMAIL>" 
                className="hover:text-accent transition-colors"
              >
                <EMAIL>
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;