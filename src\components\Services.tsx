import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Code, Globe, Briefcase } from "lucide-react";

const Services = () => {
  const services = [
    {
      icon: <Code className="w-12 h-12 text-primary" />,
      title: "Web Design & Development",
      description: "We craft responsive, lightning-fast websites that don't just look amazing — they drive results. From custom web apps to high-converting landing pages, your online presence starts here.",
      features: ["WordPress", "Next.js", "E-commerce", "Web Portals"],
      gradient: "from-blue-500 to-purple-600"
    },
    {
      icon: <Globe className="w-12 h-12 text-primary" />,
      title: "Branding & Design",
      description: "We build brands from scratch and refine existing ones — logos, visual identity, print & digital assets, all with a cohesive story.",
      features: ["Logo Design", "Brand Guidelines", "Posters & Banners", "Print Media", "T-Shirt Printing"],
      gradient: "from-purple-500 to-pink-600"
    },
    {
      icon: <Briefcase className="w-12 h-12 text-primary" />,
      title: "Digital Marketing",
      description: "Our campaigns are smart, data-driven, and designed to scale your impact across the web.",
      features: ["Social Media Strategy", "Paid Ads", "Email Marketing", "SEO", "Analytics"],
      gradient: "from-pink-500 to-red-600"
    }
  ];

  return (
    <section id="services" className="py-20 bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16 animate-fade-in-up">
          <h2 className="text-4xl lg:text-5xl font-bold text-foreground mb-6">
            What We Do
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Full-spectrum digital solutions designed to transform your business and captivate your audience.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <Card 
              key={index} 
              className="group hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border-border/50 hover:border-primary/20 animate-scale-in"
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              <CardHeader className="text-center pb-4">
                <div className="mx-auto mb-4 p-3 rounded-xl bg-primary/10 w-fit group-hover:bg-primary/20 transition-colors">
                  {service.icon}
                </div>
                <CardTitle className="text-2xl font-bold text-foreground mb-2">
                  {service.title}
                </CardTitle>
              </CardHeader>
              
              <CardContent className="text-center">
                <CardDescription className="text-muted-foreground mb-6 leading-relaxed">
                  {service.description}
                </CardDescription>
                
                <div className="flex flex-wrap gap-2 justify-center mb-6">
                  {service.features.map((feature, featureIndex) => (
                    <span 
                      key={featureIndex} 
                      className="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm font-medium"
                    >
                      {feature}
                    </span>
                  ))}
                </div>
                
                <Button 
                  variant="outline" 
                  className="w-full hover:bg-primary hover:text-primary-foreground transition-all"
                >
                  Learn More
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Why Intellinex Section */}
        <div className="mt-20 text-center">
          <h3 className="text-3xl lg:text-4xl font-bold text-foreground mb-12">
            Why Intellinex?
          </h3>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                title: "Full-Spectrum Expertise",
                description: "One team, all your digital needs."
              },
              {
                title: "International Reach",
                description: "We work across time zones, borders, and expectations."
              },
              {
                title: "Creative + Tech",
                description: "Code meets design. Strategy meets execution."
              },
              {
                title: "Client-Centered",
                description: "We listen. We collaborate. We deliver."
              }
            ].map((item, index) => (
              <div key={index} className="text-center group">
                <div className="w-16 h-16 mx-auto mb-4 bg-primary/10 rounded-xl flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                  <span className="text-2xl font-bold text-primary">✓</span>
                </div>
                <h4 className="text-lg font-semibold text-foreground mb-2">
                  {item.title}
                </h4>
                <p className="text-muted-foreground">
                  {item.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Services;