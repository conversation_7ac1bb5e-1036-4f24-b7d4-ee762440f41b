[{"C:\\projects\\intellinex-digital-forge\\app\\layout.tsx": "1", "C:\\projects\\intellinex-digital-forge\\app\\not-found.tsx": "2", "C:\\projects\\intellinex-digital-forge\\app\\page.tsx": "3", "C:\\projects\\intellinex-digital-forge\\app\\providers.tsx": "4", "C:\\projects\\intellinex-digital-forge\\src\\components\\About.tsx": "5", "C:\\projects\\intellinex-digital-forge\\src\\components\\Contact.tsx": "6", "C:\\projects\\intellinex-digital-forge\\src\\components\\Footer.tsx": "7", "C:\\projects\\intellinex-digital-forge\\src\\components\\Hero.tsx": "8", "C:\\projects\\intellinex-digital-forge\\src\\components\\Navigation.tsx": "9", "C:\\projects\\intellinex-digital-forge\\src\\components\\Services.tsx": "10", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\accordion.tsx": "11", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\alert-dialog.tsx": "12", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\alert.tsx": "13", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\aspect-ratio.tsx": "14", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\avatar.tsx": "15", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\badge.tsx": "16", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\breadcrumb.tsx": "17", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\button.tsx": "18", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\calendar.tsx": "19", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\card.tsx": "20", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\carousel.tsx": "21", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\chart.tsx": "22", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\checkbox.tsx": "23", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\collapsible.tsx": "24", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\command.tsx": "25", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\context-menu.tsx": "26", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\dialog.tsx": "27", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\drawer.tsx": "28", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\dropdown-menu.tsx": "29", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\form.tsx": "30", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\hover-card.tsx": "31", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\input-otp.tsx": "32", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\input.tsx": "33", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\label.tsx": "34", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\menubar.tsx": "35", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\navigation-menu.tsx": "36", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\pagination.tsx": "37", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\popover.tsx": "38", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\progress.tsx": "39", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\radio-group.tsx": "40", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\resizable.tsx": "41", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\scroll-area.tsx": "42", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\select.tsx": "43", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\separator.tsx": "44", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\sheet.tsx": "45", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\sidebar.tsx": "46", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\skeleton.tsx": "47", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\slider.tsx": "48", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\sonner.tsx": "49", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\switch.tsx": "50", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\table.tsx": "51", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\tabs.tsx": "52", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\textarea.tsx": "53", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\toast.tsx": "54", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\toaster.tsx": "55", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\toggle-group.tsx": "56", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\toggle.tsx": "57", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\tooltip.tsx": "58", "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\use-toast.ts": "59", "C:\\projects\\intellinex-digital-forge\\src\\hooks\\use-mobile.tsx": "60", "C:\\projects\\intellinex-digital-forge\\src\\hooks\\use-toast.ts": "61", "C:\\projects\\intellinex-digital-forge\\src\\lib\\utils.ts": "62"}, {"size": 2184, "mtime": 1750210740277, "results": "63", "hashOfConfig": "64"}, {"size": 834, "mtime": 1750210777514, "results": "65", "hashOfConfig": "64"}, {"size": 457, "mtime": 1750210767413, "results": "66", "hashOfConfig": "64"}, {"size": 460, "mtime": 1750210750034, "results": "67", "hashOfConfig": "64"}, {"size": 7838, "mtime": 1750209481986, "results": "68", "hashOfConfig": "64"}, {"size": 9791, "mtime": 1750210884687, "results": "69", "hashOfConfig": "64"}, {"size": 4830, "mtime": 1750209481986, "results": "70", "hashOfConfig": "64"}, {"size": 2565, "mtime": 1750209481986, "results": "71", "hashOfConfig": "64"}, {"size": 5350, "mtime": 1750210940770, "results": "72", "hashOfConfig": "64"}, {"size": 5574, "mtime": 1750209482002, "results": "73", "hashOfConfig": "64"}, {"size": 2033, "mtime": 1750209482002, "results": "74", "hashOfConfig": "64"}, {"size": 4559, "mtime": 1750209482002, "results": "75", "hashOfConfig": "64"}, {"size": 1643, "mtime": 1750209482002, "results": "76", "hashOfConfig": "64"}, {"size": 145, "mtime": 1750209482002, "results": "77", "hashOfConfig": "64"}, {"size": 1453, "mtime": 1750209482002, "results": "78", "hashOfConfig": "64"}, {"size": 1164, "mtime": 1750209482002, "results": "79", "hashOfConfig": "64"}, {"size": 2816, "mtime": 1750209482002, "results": "80", "hashOfConfig": "64"}, {"size": 1957, "mtime": 1750209482002, "results": "81", "hashOfConfig": "64"}, {"size": 2684, "mtime": 1750209482002, "results": "82", "hashOfConfig": "64"}, {"size": 1956, "mtime": 1750209482002, "results": "83", "hashOfConfig": "64"}, {"size": 6470, "mtime": 1750209482002, "results": "84", "hashOfConfig": "64"}, {"size": 10829, "mtime": 1750209482002, "results": "85", "hashOfConfig": "64"}, {"size": 1084, "mtime": 1750209482002, "results": "86", "hashOfConfig": "64"}, {"size": 324, "mtime": 1750209482002, "results": "87", "hashOfConfig": "64"}, {"size": 5032, "mtime": 1750209482002, "results": "88", "hashOfConfig": "64"}, {"size": 7444, "mtime": 1750209482002, "results": "89", "hashOfConfig": "64"}, {"size": 3955, "mtime": 1750209482002, "results": "90", "hashOfConfig": "64"}, {"size": 3123, "mtime": 1750209482002, "results": "91", "hashOfConfig": "64"}, {"size": 7493, "mtime": 1750209482018, "results": "92", "hashOfConfig": "64"}, {"size": 4261, "mtime": 1750209482018, "results": "93", "hashOfConfig": "64"}, {"size": 1211, "mtime": 1750209482018, "results": "94", "hashOfConfig": "64"}, {"size": 2223, "mtime": 1750209482018, "results": "95", "hashOfConfig": "64"}, {"size": 813, "mtime": 1750209482018, "results": "96", "hashOfConfig": "64"}, {"size": 734, "mtime": 1750209482018, "results": "97", "hashOfConfig": "64"}, {"size": 8208, "mtime": 1750209482018, "results": "98", "hashOfConfig": "64"}, {"size": 5174, "mtime": 1750209482018, "results": "99", "hashOfConfig": "64"}, {"size": 2868, "mtime": 1750209482018, "results": "100", "hashOfConfig": "64"}, {"size": 1259, "mtime": 1750209482018, "results": "101", "hashOfConfig": "64"}, {"size": 803, "mtime": 1750209482018, "results": "102", "hashOfConfig": "64"}, {"size": 1509, "mtime": 1750209482018, "results": "103", "hashOfConfig": "64"}, {"size": 1752, "mtime": 1750209482018, "results": "104", "hashOfConfig": "64"}, {"size": 1688, "mtime": 1750209482018, "results": "105", "hashOfConfig": "64"}, {"size": 5773, "mtime": 1750209482018, "results": "106", "hashOfConfig": "64"}, {"size": 785, "mtime": 1750209482018, "results": "107", "hashOfConfig": "64"}, {"size": 4381, "mtime": 1750209482018, "results": "108", "hashOfConfig": "64"}, {"size": 24128, "mtime": 1750209482018, "results": "109", "hashOfConfig": "64"}, {"size": 276, "mtime": 1750209482033, "results": "110", "hashOfConfig": "64"}, {"size": 1103, "mtime": 1750209482033, "results": "111", "hashOfConfig": "64"}, {"size": 923, "mtime": 1750209482033, "results": "112", "hashOfConfig": "64"}, {"size": 1166, "mtime": 1750209482033, "results": "113", "hashOfConfig": "64"}, {"size": 2882, "mtime": 1750209482033, "results": "114", "hashOfConfig": "64"}, {"size": 1936, "mtime": 1750209482033, "results": "115", "hashOfConfig": "64"}, {"size": 796, "mtime": 1750209482033, "results": "116", "hashOfConfig": "64"}, {"size": 4972, "mtime": 1750209482033, "results": "117", "hashOfConfig": "64"}, {"size": 805, "mtime": 1750209482033, "results": "118", "hashOfConfig": "64"}, {"size": 1798, "mtime": 1750209482033, "results": "119", "hashOfConfig": "64"}, {"size": 1478, "mtime": 1750209482033, "results": "120", "hashOfConfig": "64"}, {"size": 1173, "mtime": 1750209482033, "results": "121", "hashOfConfig": "64"}, {"size": 85, "mtime": 1750209482033, "results": "122", "hashOfConfig": "64"}, {"size": 584, "mtime": 1750209482033, "results": "123", "hashOfConfig": "64"}, {"size": 4086, "mtime": 1750209482033, "results": "124", "hashOfConfig": "64"}, {"size": 172, "mtime": 1750209482033, "results": "125", "hashOfConfig": "64"}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1pn5e9f", {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\projects\\intellinex-digital-forge\\app\\layout.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\app\\not-found.tsx", ["312", "313"], [], "C:\\projects\\intellinex-digital-forge\\app\\page.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\app\\providers.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\About.tsx", ["314", "315", "316", "317", "318", "319", "320"], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\Contact.tsx", ["321", "322", "323"], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\Footer.tsx", ["324"], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\Hero.tsx", ["325"], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\Navigation.tsx", ["326", "327"], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\Services.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\accordion.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\alert-dialog.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\alert.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\aspect-ratio.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\avatar.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\badge.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\breadcrumb.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\button.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\calendar.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\card.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\carousel.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\chart.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\checkbox.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\collapsible.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\command.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\context-menu.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\dialog.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\drawer.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\form.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\hover-card.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\input-otp.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\input.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\label.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\menubar.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\navigation-menu.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\pagination.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\popover.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\progress.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\radio-group.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\resizable.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\scroll-area.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\select.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\separator.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\sheet.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\sidebar.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\skeleton.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\slider.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\sonner.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\switch.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\table.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\tabs.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\textarea.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\toast.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\toaster.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\toggle-group.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\toggle.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\tooltip.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\components\\ui\\use-toast.ts", [], [], "C:\\projects\\intellinex-digital-forge\\src\\hooks\\use-mobile.tsx", [], [], "C:\\projects\\intellinex-digital-forge\\src\\hooks\\use-toast.ts", [], [], "C:\\projects\\intellinex-digital-forge\\src\\lib\\utils.ts", [], [], {"ruleId": "328", "severity": 2, "message": "329", "line": 11, "column": 27, "nodeType": "330", "messageId": "331", "suggestions": "332"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 11, "column": 47, "nodeType": "330", "messageId": "331", "suggestions": "333"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 42, "column": 66, "nodeType": "330", "messageId": "331", "suggestions": "334"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 45, "column": 103, "nodeType": "330", "messageId": "331", "suggestions": "335"}, {"ruleId": "328", "severity": 2, "message": "336", "line": 145, "column": 21, "nodeType": "330", "messageId": "331", "suggestions": "337"}, {"ruleId": "328", "severity": 2, "message": "336", "line": 146, "column": 70, "nodeType": "330", "messageId": "331", "suggestions": "338"}, {"ruleId": "328", "severity": 2, "message": "336", "line": 163, "column": 21, "nodeType": "330", "messageId": "331", "suggestions": "339"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 163, "column": 31, "nodeType": "330", "messageId": "331", "suggestions": "340"}, {"ruleId": "328", "severity": 2, "message": "336", "line": 163, "column": 82, "nodeType": "330", "messageId": "331", "suggestions": "341"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 18, "column": 16, "nodeType": "330", "messageId": "331", "suggestions": "342"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 30, "column": 56, "nodeType": "330", "messageId": "331", "suggestions": "343"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 172, "column": 25, "nodeType": "330", "messageId": "331", "suggestions": "344"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 16, "column": 61, "nodeType": "330", "messageId": "331", "suggestions": "345"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 33, "column": 18, "nodeType": "330", "messageId": "331", "suggestions": "346"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 70, "column": 18, "nodeType": "330", "messageId": "331", "suggestions": "347"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 138, "column": 20, "nodeType": "330", "messageId": "331", "suggestions": "348"}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["349", "350", "351", "352"], ["353", "354", "355", "356"], ["357", "358", "359", "360"], ["361", "362", "363", "364"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["365", "366", "367", "368"], ["369", "370", "371", "372"], ["373", "374", "375", "376"], ["377", "378", "379", "380"], ["381", "382", "383", "384"], ["385", "386", "387", "388"], ["389", "390", "391", "392"], ["393", "394", "395", "396"], ["397", "398", "399", "400"], ["401", "402", "403", "404"], ["405", "406", "407", "408"], ["409", "410", "411", "412"], {"messageId": "413", "data": "414", "fix": "415", "desc": "416"}, {"messageId": "413", "data": "417", "fix": "418", "desc": "419"}, {"messageId": "413", "data": "420", "fix": "421", "desc": "422"}, {"messageId": "413", "data": "423", "fix": "424", "desc": "425"}, {"messageId": "413", "data": "426", "fix": "427", "desc": "416"}, {"messageId": "413", "data": "428", "fix": "429", "desc": "419"}, {"messageId": "413", "data": "430", "fix": "431", "desc": "422"}, {"messageId": "413", "data": "432", "fix": "433", "desc": "425"}, {"messageId": "413", "data": "434", "fix": "435", "desc": "416"}, {"messageId": "413", "data": "436", "fix": "437", "desc": "419"}, {"messageId": "413", "data": "438", "fix": "439", "desc": "422"}, {"messageId": "413", "data": "440", "fix": "441", "desc": "425"}, {"messageId": "413", "data": "442", "fix": "443", "desc": "416"}, {"messageId": "413", "data": "444", "fix": "445", "desc": "419"}, {"messageId": "413", "data": "446", "fix": "447", "desc": "422"}, {"messageId": "413", "data": "448", "fix": "449", "desc": "425"}, {"messageId": "413", "data": "450", "fix": "451", "desc": "452"}, {"messageId": "413", "data": "453", "fix": "454", "desc": "455"}, {"messageId": "413", "data": "456", "fix": "457", "desc": "458"}, {"messageId": "413", "data": "459", "fix": "460", "desc": "461"}, {"messageId": "413", "data": "462", "fix": "463", "desc": "452"}, {"messageId": "413", "data": "464", "fix": "465", "desc": "455"}, {"messageId": "413", "data": "466", "fix": "467", "desc": "458"}, {"messageId": "413", "data": "468", "fix": "469", "desc": "461"}, {"messageId": "413", "data": "470", "fix": "471", "desc": "452"}, {"messageId": "413", "data": "472", "fix": "473", "desc": "455"}, {"messageId": "413", "data": "474", "fix": "475", "desc": "458"}, {"messageId": "413", "data": "476", "fix": "477", "desc": "461"}, {"messageId": "413", "data": "478", "fix": "479", "desc": "416"}, {"messageId": "413", "data": "480", "fix": "481", "desc": "419"}, {"messageId": "413", "data": "482", "fix": "483", "desc": "422"}, {"messageId": "413", "data": "484", "fix": "485", "desc": "425"}, {"messageId": "413", "data": "486", "fix": "487", "desc": "452"}, {"messageId": "413", "data": "488", "fix": "489", "desc": "455"}, {"messageId": "413", "data": "490", "fix": "491", "desc": "458"}, {"messageId": "413", "data": "492", "fix": "493", "desc": "461"}, {"messageId": "413", "data": "494", "fix": "495", "desc": "416"}, {"messageId": "413", "data": "496", "fix": "497", "desc": "419"}, {"messageId": "413", "data": "498", "fix": "499", "desc": "422"}, {"messageId": "413", "data": "500", "fix": "501", "desc": "425"}, {"messageId": "413", "data": "502", "fix": "503", "desc": "416"}, {"messageId": "413", "data": "504", "fix": "505", "desc": "419"}, {"messageId": "413", "data": "506", "fix": "507", "desc": "422"}, {"messageId": "413", "data": "508", "fix": "509", "desc": "425"}, {"messageId": "413", "data": "510", "fix": "511", "desc": "416"}, {"messageId": "413", "data": "512", "fix": "513", "desc": "419"}, {"messageId": "413", "data": "514", "fix": "515", "desc": "422"}, {"messageId": "413", "data": "516", "fix": "517", "desc": "425"}, {"messageId": "413", "data": "518", "fix": "519", "desc": "416"}, {"messageId": "413", "data": "520", "fix": "521", "desc": "419"}, {"messageId": "413", "data": "522", "fix": "523", "desc": "422"}, {"messageId": "413", "data": "524", "fix": "525", "desc": "425"}, {"messageId": "413", "data": "526", "fix": "527", "desc": "416"}, {"messageId": "413", "data": "528", "fix": "529", "desc": "419"}, {"messageId": "413", "data": "530", "fix": "531", "desc": "422"}, {"messageId": "413", "data": "532", "fix": "533", "desc": "425"}, {"messageId": "413", "data": "534", "fix": "535", "desc": "416"}, {"messageId": "413", "data": "536", "fix": "537", "desc": "419"}, {"messageId": "413", "data": "538", "fix": "539", "desc": "422"}, {"messageId": "413", "data": "540", "fix": "541", "desc": "425"}, {"messageId": "413", "data": "542", "fix": "543", "desc": "416"}, {"messageId": "413", "data": "544", "fix": "545", "desc": "419"}, {"messageId": "413", "data": "546", "fix": "547", "desc": "422"}, {"messageId": "413", "data": "548", "fix": "549", "desc": "425"}, "replaceWithAlt", {"alt": "550"}, {"range": "551", "text": "552"}, "Replace with `&apos;`.", {"alt": "553"}, {"range": "554", "text": "555"}, "Replace with `&lsquo;`.", {"alt": "556"}, {"range": "557", "text": "558"}, "Replace with `&#39;`.", {"alt": "559"}, {"range": "560", "text": "561"}, "Replace with `&rsquo;`.", {"alt": "550"}, {"range": "562", "text": "563"}, {"alt": "553"}, {"range": "564", "text": "565"}, {"alt": "556"}, {"range": "566", "text": "567"}, {"alt": "559"}, {"range": "568", "text": "569"}, {"alt": "550"}, {"range": "570", "text": "571"}, {"alt": "553"}, {"range": "572", "text": "573"}, {"alt": "556"}, {"range": "574", "text": "575"}, {"alt": "559"}, {"range": "576", "text": "577"}, {"alt": "550"}, {"range": "578", "text": "579"}, {"alt": "553"}, {"range": "580", "text": "581"}, {"alt": "556"}, {"range": "582", "text": "583"}, {"alt": "559"}, {"range": "584", "text": "585"}, {"alt": "586"}, {"range": "587", "text": "588"}, "Replace with `&quot;`.", {"alt": "589"}, {"range": "590", "text": "591"}, "Replace with `&ldquo;`.", {"alt": "592"}, {"range": "593", "text": "594"}, "Replace with `&#34;`.", {"alt": "595"}, {"range": "596", "text": "597"}, "Replace with `&rdquo;`.", {"alt": "586"}, {"range": "598", "text": "599"}, {"alt": "589"}, {"range": "600", "text": "601"}, {"alt": "592"}, {"range": "602", "text": "603"}, {"alt": "595"}, {"range": "604", "text": "605"}, {"alt": "586"}, {"range": "606", "text": "607"}, {"alt": "589"}, {"range": "608", "text": "609"}, {"alt": "592"}, {"range": "610", "text": "611"}, {"alt": "595"}, {"range": "612", "text": "613"}, {"alt": "550"}, {"range": "614", "text": "615"}, {"alt": "553"}, {"range": "616", "text": "617"}, {"alt": "556"}, {"range": "618", "text": "619"}, {"alt": "559"}, {"range": "620", "text": "621"}, {"alt": "586"}, {"range": "622", "text": "623"}, {"alt": "589"}, {"range": "624", "text": "625"}, {"alt": "592"}, {"range": "626", "text": "627"}, {"alt": "595"}, {"range": "628", "text": "629"}, {"alt": "550"}, {"range": "630", "text": "631"}, {"alt": "553"}, {"range": "632", "text": "633"}, {"alt": "556"}, {"range": "634", "text": "635"}, {"alt": "559"}, {"range": "636", "text": "637"}, {"alt": "550"}, {"range": "638", "text": "639"}, {"alt": "553"}, {"range": "640", "text": "641"}, {"alt": "556"}, {"range": "642", "text": "643"}, {"alt": "559"}, {"range": "644", "text": "645"}, {"alt": "550"}, {"range": "646", "text": "647"}, {"alt": "553"}, {"range": "648", "text": "649"}, {"alt": "556"}, {"range": "650", "text": "651"}, {"alt": "559"}, {"range": "652", "text": "653"}, {"alt": "550"}, {"range": "654", "text": "655"}, {"alt": "553"}, {"range": "656", "text": "657"}, {"alt": "556"}, {"range": "658", "text": "659"}, {"alt": "559"}, {"range": "660", "text": "661"}, {"alt": "550"}, {"range": "662", "text": "663"}, {"alt": "553"}, {"range": "664", "text": "665"}, {"alt": "556"}, {"range": "666", "text": "667"}, {"alt": "559"}, {"range": "668", "text": "669"}, {"alt": "550"}, {"range": "670", "text": "663"}, {"alt": "553"}, {"range": "671", "text": "665"}, {"alt": "556"}, {"range": "672", "text": "667"}, {"alt": "559"}, {"range": "673", "text": "669"}, {"alt": "550"}, {"range": "674", "text": "675"}, {"alt": "553"}, {"range": "676", "text": "677"}, {"alt": "556"}, {"range": "678", "text": "679"}, {"alt": "559"}, {"range": "680", "text": "681"}, "&apos;", [461, 609], "\n          Sorry, we couldn&apos;t find the page you're looking for. \n          It might have been moved, deleted, or you entered the wrong URL.\n        ", "&lsquo;", [461, 609], "\n          Sorry, we couldn&lsquo;t find the page you're looking for. \n          It might have been moved, deleted, or you entered the wrong URL.\n        ", "&#39;", [461, 609], "\n          Sorry, we couldn&#39;t find the page you're looking for. \n          It might have been moved, deleted, or you entered the wrong URL.\n        ", "&rsquo;", [461, 609], "\n          Sorry, we couldn&rsquo;t find the page you're looking for. \n          It might have been moved, deleted, or you entered the wrong URL.\n        ", [461, 609], "\n          Sorry, we couldn't find the page you&apos;re looking for. \n          It might have been moved, deleted, or you entered the wrong URL.\n        ", [461, 609], "\n          Sorry, we couldn't find the page you&lsquo;re looking for. \n          It might have been moved, deleted, or you entered the wrong URL.\n        ", [461, 609], "\n          Sorry, we couldn't find the page you&#39;re looking for. \n          It might have been moved, deleted, or you entered the wrong URL.\n        ", [461, 609], "\n          Sorry, we couldn't find the page you&rsquo;re looking for. \n          It might have been moved, deleted, or you entered the wrong URL.\n        ", [1572, 1796], "\r\n              Intellinex was born out of one opportunity — and a bigger vision. \r\n              We started with one international client. Today, we&apos;re building digital experiences for brands around the globe.\r\n            ", [1572, 1796], "\r\n              Intellinex was born out of one opportunity — and a bigger vision. \r\n              We started with one international client. Today, we&lsquo;re building digital experiences for brands around the globe.\r\n            ", [1572, 1796], "\r\n              Intellinex was born out of one opportunity — and a bigger vision. \r\n              We started with one international client. Today, we&#39;re building digital experiences for brands around the globe.\r\n            ", [1572, 1796], "\r\n              Intellinex was born out of one opportunity — and a bigger vision. \r\n              We started with one international client. Today, we&rsquo;re building digital experiences for brands around the globe.\r\n            ", [1875, 2038], "\r\n              Our team combines technical expertise with creative vision to deliver solutions that don&apos;t just meet expectations — they exceed them.\r\n            ", [1875, 2038], "\r\n              Our team combines technical expertise with creative vision to deliver solutions that don&lsquo;t just meet expectations — they exceed them.\r\n            ", [1875, 2038], "\r\n              Our team combines technical expertise with creative vision to deliver solutions that don&#39;t just meet expectations — they exceed them.\r\n            ", [1875, 2038], "\r\n              Our team combines technical expertise with creative vision to deliver solutions that don&rsquo;t just meet expectations — they exceed them.\r\n            ", "&quot;", [6492, 6682], "\r\n                    &quot;We were looking for a freelance developer. What we found in Intellinex was \r\n                    a whole ecosystem of ideas, design, and strategy.\"\r\n                  ", "&ldquo;", [6492, 6682], "\r\n                    &ldquo;We were looking for a freelance developer. What we found in Intellinex was \r\n                    a whole ecosystem of ideas, design, and strategy.\"\r\n                  ", "&#34;", [6492, 6682], "\r\n                    &#34;We were looking for a freelance developer. What we found in Intellinex was \r\n                    a whole ecosystem of ideas, design, and strategy.\"\r\n                  ", "&rdquo;", [6492, 6682], "\r\n                    &rdquo;We were looking for a freelance developer. What we found in Intellinex was \r\n                    a whole ecosystem of ideas, design, and strategy.\"\r\n                  ", [6492, 6682], "\r\n                    \"We were looking for a freelance developer. What we found in Intellinex was \r\n                    a whole ecosystem of ideas, design, and strategy.&quot;\r\n                  ", [6492, 6682], "\r\n                    \"We were looking for a freelance developer. What we found in Intellinex was \r\n                    a whole ecosystem of ideas, design, and strategy.&ldquo;\r\n                  ", [6492, 6682], "\r\n                    \"We were looking for a freelance developer. What we found in Intellinex was \r\n                    a whole ecosystem of ideas, design, and strategy.&#34;\r\n                  ", [6492, 6682], "\r\n                    \"We were looking for a freelance developer. What we found in Intellinex was \r\n                    a whole ecosystem of ideas, design, and strategy.&rdquo;\r\n                  ", [7317, 7421], "\r\n                    &quot;They didn't just build us a website — they built us a brand.\"\r\n                  ", [7317, 7421], "\r\n                    &ldquo;They didn't just build us a website — they built us a brand.\"\r\n                  ", [7317, 7421], "\r\n                    &#34;They didn't just build us a website — they built us a brand.\"\r\n                  ", [7317, 7421], "\r\n                    &rdquo;They didn't just build us a website — they built us a brand.\"\r\n                  ", [7317, 7421], "\r\n                    \"They didn&apos;t just build us a website — they built us a brand.\"\r\n                  ", [7317, 7421], "\r\n                    \"They didn&lsquo;t just build us a website — they built us a brand.\"\r\n                  ", [7317, 7421], "\r\n                    \"They didn&#39;t just build us a website — they built us a brand.\"\r\n                  ", [7317, 7421], "\r\n                    \"They didn&rsquo;t just build us a website — they built us a brand.\"\r\n                  ", [7317, 7421], "\r\n                    \"They didn't just build us a website — they built us a brand.&quot;\r\n                  ", [7317, 7421], "\r\n                    \"They didn't just build us a website — they built us a brand.&ldquo;\r\n                  ", [7317, 7421], "\r\n                    \"They didn't just build us a website — they built us a brand.&#34;\r\n                  ", [7317, 7421], "\r\n                    \"They didn't just build us a website — they built us a brand.&rdquo;\r\n                  ", [749, 836], "\r\n            Let&apos;s turn your ideas into smart, stunning digital solutions.\r\n          ", [749, 836], "\r\n            Let&lsquo;s turn your ideas into smart, stunning digital solutions.\r\n          ", [749, 836], "\r\n            Let&#39;s turn your ideas into smart, stunning digital solutions.\r\n          ", [749, 836], "\r\n            Let&rsquo;s turn your ideas into smart, stunning digital solutions.\r\n          ", [1234, 1337], "\r\n                Tell us about your project or idea — we&apos;re ready to bring it to life.\r\n              ", [1234, 1337], "\r\n                Tell us about your project or idea — we&lsquo;re ready to bring it to life.\r\n              ", [1234, 1337], "\r\n                Tell us about your project or idea — we&#39;re ready to bring it to life.\r\n              ", [1234, 1337], "\r\n                Tell us about your project or idea — we&rsquo;re ready to bring it to life.\r\n              ", [7516, 7589], "\r\n                      We&apos;re here when you need us\r\n                    ", [7516, 7589], "\r\n                      We&lsquo;re here when you need us\r\n                    ", [7516, 7589], "\r\n                      We&#39;re here when you need us\r\n                    ", [7516, 7589], "\r\n                      We&rsquo;re here when you need us\r\n                    ", [661, 858], "\r\n              Your global partner in creating powerful digital solutions that inspire and convert. \r\n              From web design to branding and marketing — we&apos;ve got you covered.\r\n            ", [661, 858], "\r\n              Your global partner in creating powerful digital solutions that inspire and convert. \r\n              From web design to branding and marketing — we&lsquo;ve got you covered.\r\n            ", [661, 858], "\r\n              Your global partner in creating powerful digital solutions that inspire and convert. \r\n              From web design to branding and marketing — we&#39;ve got you covered.\r\n            ", [661, 858], "\r\n              Your global partner in creating powerful digital solutions that inspire and convert. \r\n              From web design to branding and marketing — we&rsquo;ve got you covered.\r\n            ", [1483, 1533], "\r\n              Let&apos;s Build Together\r\n            ", [1483, 1533], "\r\n              Let&lsquo;s Build Together\r\n            ", [1483, 1533], "\r\n              Let&#39;s Build Together\r\n            ", [1483, 1533], "\r\n              Let&rsquo;s Build Together\r\n            ", [2639, 2689], [2639, 2689], [2639, 2689], [2639, 2689], [5171, 5225], "\r\n                Let&apos;s Build Together\r\n              ", [5171, 5225], "\r\n                Let&lsquo;s Build Together\r\n              ", [5171, 5225], "\r\n                Let&#39;s Build Together\r\n              ", [5171, 5225], "\r\n                Let&rsquo;s Build Together\r\n              "]